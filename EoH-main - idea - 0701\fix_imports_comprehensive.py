#!/usr/bin/env python3
"""
全面修复导入语句 - 修复所有模块中的导入问题
"""

import os
import re

def fix_imports_in_file(file_path):
    """修复单个文件中的导入语句"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复函数调用中的模块引用
        function_call_fixes = [
            (r'gls_evol_enhanced\.tour_cost', 'tour_cost'),
            (r'gls_evol_enhanced\.tour2route', 'tour2route'),
            (r'gls_run\.solve_instance', 'solve_instance'),
            (r'utils\.', 'utils.'),  # 保持utils模块的调用方式
        ]
        
        for old_pattern, new_pattern in function_call_fixes:
            content = re.sub(old_pattern, new_pattern, content)
        
        # 修复导入语句
        import_fixes = [
            # 修复相对导入
            (r'from \.\.', 'from '),
            (r'from \.', 'from '),
            
            # 修复具体的导入问题
            (r'from core\.algorithms import gls_evol_enhanced', 'from core.algorithms.gls_evol_enhanced import tour_cost, tour2route'),
            (r'from core\.algorithms import gls_run', 'from core.algorithms.gls_run import solve_instance'),
            (r'import gls_evol_enhanced', 'from core.algorithms import gls_evol_enhanced'),
            (r'import gls_run', 'from core.algorithms import gls_run'),
        ]
        
        for old_pattern, new_pattern in import_fixes:
            content = re.sub(old_pattern, new_pattern, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"修复导入: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"修复文件失败 {file_path}: {e}")
        return False

def fix_all_imports():
    """修复所有Python文件的导入语句"""
    
    directories_to_fix = ['src']
    
    fixed_files = 0
    total_files = 0
    
    for directory in directories_to_fix:
        if os.path.exists(directory):
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        total_files += 1
                        if fix_imports_in_file(file_path):
                            fixed_files += 1
    
    print(f"导入修复完成: 修复了 {fixed_files}/{total_files} 个文件")

if __name__ == "__main__":
    fix_all_imports()
