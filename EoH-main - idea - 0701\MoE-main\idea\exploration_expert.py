"""
探索专家模块

包含ExplorationExpert类，负责为分配了探索策略的个体生成多样化的新路径。
使用纯算法实现，不依赖LLM。
"""

import copy
import random
import numpy as np
from expert_base import ExpertBase
from gls_evol_enhanced import tour_cost


class ExplorationExpert(ExpertBase):
    """探索路径生成专家，为分配了探索策略的个体生成多样化的新路径
    使用纯算法实现，不依赖LLM"""

    def __init__(self, interface_llm=None):
        super().__init__()
        # 保留接口参数以保持兼容性，但不使用
        self.interface_llm = interface_llm

        # 探索路径生成的参数配置
        self.diversity_weight = 0.7  # 多样性权重
        self.quality_weight = 0.3    # 质量权重
        self.exploration_radius = 0.3  # 探索半径（相对于问题规模）
        self.knowledge_base = None  # 暂不实现知识库
        
    def calculate_path_cost(self, tour, distance_matrix):
        """计算路径的总成本
        
        参数:
            tour (list): 旅行路径
            distance_matrix (numpy.ndarray): 距离矩阵
            
        返回:
            float: 路径总成本
        """
        self.logger.info("计算路径成本")
        return tour_cost(distance_matrix, tour)
    
    def generate_path(self, individual, landscape_report, populations, distance_matrix, individual_index, evo_populations=None, res_populations=None):
        """生成探索路径，使用纯算法实现，不依赖LLM

        参数:
            individual: 当前个体
            landscape_report: 景观分析报告（保留接口兼容性）
            populations: 当前种群
            distance_matrix: 距离矩阵
            individual_index: 个体在种群中的索引
            evo_populations: 进化种群，用于存储生成的路径
            res_populations: 精英解种群，用于存储高质量解
        """
        self.logger.info(f"开始为个体 {individual_index} 生成探索路径（算法实现）")

        # 初始化种群列表（如果未提供）
        if populations is None:
            populations = []
        if res_populations is None:
            res_populations = []

        try:
            # 使用算法生成多样化探索路径
            new_tour = self.generate_diverse_exploration_path(
                current_individual=individual,
                populations=populations,
                distance_matrix=distance_matrix,
                individual_index=individual_index
            )

            # 计算新路径的成本
            new_cost = self.calculate_path_cost(new_tour, distance_matrix)

            # 创建探索路径结果
            exploration_path = {
                "new_tour": new_tour,
                "cur_cost": new_cost
            }

            # 创建进化个体的副本
            evo_individual = copy.deepcopy(individual)
            evo_individual["tour"] = new_tour
            evo_individual["cur_cost"] = new_cost

            # 更新种群中对应位置的路径
            populations[individual_index]["tour"] = evo_individual["tour"]
            populations[individual_index]["cur_cost"] = evo_individual["cur_cost"]

            self.logger.info(f"探索路径生成完成，成本: {new_cost}, 路径长度: {len(new_tour)}")
            return exploration_path

        except Exception as e:
            self.logger.error(f"生成探索路径时发生错误: {e}")
            # 使用备选方案：随机扰动
            backup_tour = self.generate_random_exploration_path(individual["tour"], distance_matrix)
            backup_cost = self.calculate_path_cost(backup_tour, distance_matrix)

            backup_path = {
                "new_tour": backup_tour,
                "cur_cost": backup_cost
            }

            # 更新种群
            populations[individual_index]["tour"] = backup_tour
            populations[individual_index]["cur_cost"] = backup_cost

            self.logger.info(f"使用备选探索路径，成本: {backup_cost}")
            return backup_path

    def generate_diverse_exploration_path(self, current_individual, populations, distance_matrix, individual_index):
        """生成多样化的探索路径，基于多样性原则和区域覆盖

        参数:
            current_individual: 当前个体
            populations: 当前种群
            distance_matrix: 距离矩阵
            individual_index: 个体索引

        返回:
            新的探索路径
        """
        self.logger.info("开始生成多样化探索路径")

        # 获取问题规模
        n_cities = len(current_individual["tour"])

        # 分析当前种群的覆盖情况
        coverage_info = self.analyze_population_coverage(populations, distance_matrix)

        # 选择探索策略
        strategy = self.select_exploration_strategy(current_individual, populations, coverage_info)

        if strategy == "diversification":
            new_path = self.generate_diversification_path(current_individual["tour"], populations, distance_matrix)
        elif strategy == "region_exploration":
            new_path = self.generate_region_exploration_path(current_individual["tour"], coverage_info, distance_matrix)
        elif strategy == "hybrid_construction":
            new_path = self.generate_hybrid_construction_path(current_individual["tour"], populations, distance_matrix)
        else:  # random_restart
            new_path = self.generate_random_restart_path(n_cities, distance_matrix)

        # 验证和修复路径
        new_path = self.validate_and_repair_path(new_path, n_cities)

        self.logger.info(f"使用策略 {strategy} 生成探索路径，长度: {len(new_path)}")
        return new_path

    def analyze_population_coverage(self, populations, distance_matrix):
        """分析当前种群的区域覆盖情况

        参数:
            populations: 当前种群
            distance_matrix: 距离矩阵

        返回:
            覆盖信息字典
        """
        n_cities = distance_matrix.shape[0]

        # 统计边的使用频率
        edge_usage = {}
        for individual in populations:
            tour = individual["tour"]
            for i in range(len(tour)):
                edge = tuple(sorted([tour[i], tour[(i + 1) % len(tour)]]))
                edge_usage[edge] = edge_usage.get(edge, 0) + 1

        # 识别高频边和低频边
        total_edges = len(edge_usage)
        avg_usage = sum(edge_usage.values()) / total_edges if total_edges > 0 else 0

        high_freq_edges = {edge: count for edge, count in edge_usage.items() if count > avg_usage * 1.5}
        low_freq_edges = {edge: count for edge, count in edge_usage.items() if count < avg_usage * 0.5}

        # 识别未探索的区域（很少使用的城市对）
        unexplored_regions = []
        for i in range(n_cities):
            for j in range(i + 1, n_cities):
                edge = (i, j)
                if edge not in edge_usage or edge_usage[edge] <= 1:
                    unexplored_regions.append(edge)

        return {
            "edge_usage": edge_usage,
            "high_freq_edges": high_freq_edges,
            "low_freq_edges": low_freq_edges,
            "unexplored_regions": unexplored_regions[:min(20, len(unexplored_regions))],  # 限制数量
            "avg_usage": avg_usage
        }

    def select_exploration_strategy(self, current_individual, populations, coverage_info):
        """选择探索策略

        参数:
            current_individual: 当前个体
            populations: 当前种群
            coverage_info: 覆盖信息

        返回:
            选择的策略名称
        """
        # 计算种群多样性
        diversity = self.calculate_population_diversity(populations)

        # 根据多样性和覆盖情况选择策略
        if diversity < 0.3:  # 低多样性，需要大幅度探索
            strategies = ["diversification", "random_restart", "region_exploration"]
            weights = [0.4, 0.4, 0.2]
        elif len(coverage_info["unexplored_regions"]) > 10:  # 有很多未探索区域
            strategies = ["region_exploration", "hybrid_construction", "diversification"]
            weights = [0.5, 0.3, 0.2]
        else:  # 平衡探索
            strategies = ["hybrid_construction", "diversification", "region_exploration"]
            weights = [0.4, 0.3, 0.3]

        return random.choices(strategies, weights=weights, k=1)[0]

    def calculate_population_diversity(self, populations):
        """计算种群多样性

        参数:
            populations: 当前种群

        返回:
            多样性值 (0-1)
        """
        if len(populations) < 2:
            return 1.0

        # 计算路径间的平均汉明距离
        total_distance = 0
        comparisons = 0

        for i in range(len(populations)):
            for j in range(i + 1, len(populations)):
                tour1 = populations[i]["tour"]
                tour2 = populations[j]["tour"]

                # 计算汉明距离（不同位置的数量）
                distance = sum(1 for a, b in zip(tour1, tour2) if a != b)
                total_distance += distance
                comparisons += 1

        if comparisons == 0:
            return 1.0

        avg_distance = total_distance / comparisons
        max_possible_distance = len(populations[0]["tour"])

        return min(1.0, avg_distance / max_possible_distance)

    def generate_diversification_path(self, current_tour, populations, distance_matrix):
        """生成多样化路径，最大化与现有种群的差异

        参数:
            current_tour: 当前路径
            populations: 当前种群
            distance_matrix: 距离矩阵

        返回:
            新的多样化路径
        """
        n_cities = len(current_tour)

        # 统计种群中每个位置上城市的出现频率
        position_freq = {}
        for individual in populations:
            tour = individual["tour"]
            for pos, city in enumerate(tour):
                if pos not in position_freq:
                    position_freq[pos] = {}
                position_freq[pos][city] = position_freq[pos].get(city, 0) + 1

        # 构建新路径，优先选择在每个位置上出现频率较低的城市
        new_tour = []
        used_cities = set()

        for pos in range(n_cities):
            if pos in position_freq:
                # 获取该位置上城市的频率，选择频率最低的未使用城市
                city_freq = position_freq[pos]
                available_cities = [(city, freq) for city, freq in city_freq.items() if city not in used_cities]

                if available_cities:
                    # 按频率排序，选择频率较低的城市
                    available_cities.sort(key=lambda x: x[1])
                    # 从频率最低的前30%中随机选择
                    top_candidates = available_cities[:max(1, len(available_cities) // 3)]
                    selected_city = random.choice(top_candidates)[0]
                    new_tour.append(selected_city)
                    used_cities.add(selected_city)
                    continue

            # 如果没有频率信息或所有城市都已使用，随机选择未使用的城市
            available_cities = [city for city in range(n_cities) if city not in used_cities]
            if available_cities:
                selected_city = random.choice(available_cities)
                new_tour.append(selected_city)
                used_cities.add(selected_city)

        return new_tour

    def generate_region_exploration_path(self, current_tour, coverage_info, distance_matrix):
        """生成区域探索路径，重点探索未充分覆盖的区域

        参数:
            current_tour: 当前路径
            coverage_info: 覆盖信息
            distance_matrix: 距离矩阵

        返回:
            新的区域探索路径
        """
        n_cities = len(current_tour)

        # 获取未探索的区域
        unexplored_regions = coverage_info["unexplored_regions"]

        if not unexplored_regions:
            # 如果没有未探索区域，使用随机重启
            return self.generate_random_restart_path(n_cities, distance_matrix)

        # 选择几个未探索的区域作为构建路径的核心
        num_core_regions = min(3, len(unexplored_regions))
        core_regions = random.sample(unexplored_regions, num_core_regions)

        # 从核心区域开始构建路径
        new_tour = []
        used_cities = set()

        # 添加核心区域的城市
        for region in core_regions:
            for city in region:
                if city not in used_cities:
                    new_tour.append(city)
                    used_cities.add(city)

        # 使用最近邻启发式填充剩余城市
        while len(new_tour) < n_cities:
            if not new_tour:
                # 如果路径为空，随机选择一个起始城市
                start_city = random.randint(0, n_cities - 1)
                new_tour.append(start_city)
                used_cities.add(start_city)
                continue

            current_city = new_tour[-1]
            available_cities = [city for city in range(n_cities) if city not in used_cities]

            if not available_cities:
                break

            # 选择距离当前城市最近的未访问城市
            nearest_city = min(available_cities, key=lambda city: distance_matrix[current_city][city])
            new_tour.append(nearest_city)
            used_cities.add(nearest_city)

        return new_tour

    def generate_hybrid_construction_path(self, current_tour, populations, distance_matrix):
        """生成混合构建路径，结合贪心和多样性策略

        参数:
            current_tour: 当前路径
            populations: 当前种群
            distance_matrix: 距离矩阵

        返回:
            新的混合构建路径
        """
        n_cities = len(current_tour)

        # 随机选择起始城市
        start_city = random.randint(0, n_cities - 1)
        new_tour = [start_city]
        used_cities = {start_city}

        while len(new_tour) < n_cities:
            current_city = new_tour[-1]
            available_cities = [city for city in range(n_cities) if city not in used_cities]

            if not available_cities:
                break

            # 计算每个可用城市的评分（距离 + 多样性）
            city_scores = []
            for city in available_cities:
                # 距离分数（越近越好）
                distance_score = distance_matrix[current_city][city]

                # 多样性分数（与种群中路径的差异）
                diversity_score = 0
                for individual in populations:
                    tour = individual["tour"]
                    if len(tour) > len(new_tour):
                        # 检查在相同位置上是否选择了不同的城市
                        if tour[len(new_tour)] != city:
                            diversity_score += 1

                # 综合评分（距离权重 + 多样性权重）
                total_score = self.quality_weight * distance_score - self.diversity_weight * diversity_score
                city_scores.append((city, total_score))

            # 选择评分最好的城市（考虑一定的随机性）
            city_scores.sort(key=lambda x: x[1])
            # 从最好的前50%中随机选择
            top_candidates = city_scores[:max(1, len(city_scores) // 2)]
            selected_city = random.choice(top_candidates)[0]

            new_tour.append(selected_city)
            used_cities.add(selected_city)

        return new_tour

    def generate_random_restart_path(self, n_cities, distance_matrix):
        """生成随机重启路径

        参数:
            n_cities: 城市数量
            distance_matrix: 距离矩阵

        返回:
            新的随机路径
        """
        # 创建城市列表并随机打乱
        cities = list(range(n_cities))
        random.shuffle(cities)

        return cities

    def generate_random_exploration_path(self, current_tour, distance_matrix):
        """生成随机探索路径（备选方案）

        参数:
            current_tour: 当前路径
            distance_matrix: 距离矩阵

        返回:
            新的随机探索路径
        """
        # 复制当前路径
        new_tour = current_tour.copy()

        # 执行多次随机交换
        n_swaps = max(2, len(new_tour) // 4)
        for _ in range(n_swaps):
            i, j = random.sample(range(len(new_tour)), 2)
            new_tour[i], new_tour[j] = new_tour[j], new_tour[i]

        return new_tour

    def validate_and_repair_path(self, path, n_cities):
        """验证和修复路径，确保满足TSP约束

        参数:
            path: 待验证的路径
            n_cities: 城市数量

        返回:
            修复后的有效路径
        """
        if not path:
            # 如果路径为空，创建随机路径
            cities = list(range(n_cities))
            random.shuffle(cities)
            return cities

        # 确保所有城市都在有效范围内
        valid_path = []
        for city in path:
            if isinstance(city, (int, float)) and 0 <= city < n_cities:
                valid_path.append(int(city))

        # 移除重复城市，保留第一次出现的
        seen = set()
        unique_path = []
        for city in valid_path:
            if city not in seen:
                unique_path.append(city)
                seen.add(city)

        # 添加缺失的城市
        missing_cities = [city for city in range(n_cities) if city not in seen]
        random.shuffle(missing_cities)
        unique_path.extend(missing_cities)

        # 确保路径长度正确
        if len(unique_path) > n_cities:
            unique_path = unique_path[:n_cities]
        elif len(unique_path) < n_cities:
            # 如果仍然缺少城市，用随机城市填充
            while len(unique_path) < n_cities:
                unique_path.append(random.randint(0, n_cities - 1))

        return unique_path

    def _update_populations(self, evo_individual, populations, res_populations):
        """更新进化种群和精英解种群

        参数:
            evo_individual: 当前进化个体
            populations: 进化种群
            res_populations: 精英解种群
        """
        # 使用gls_evol_enhanced中的函数
        from gls_evol_enhanced import share_distance_o2a, is_tsplib_instance, calculate_path_similarity

        tour = evo_individual["tour"]
        cur_cost = evo_individual["cur_cost"]

        # 提取种群中的路径
        tours = [indival["tour"] for indival in populations]
        in_tours_flag, index = share_distance_o2a(tour, tours)

        # 提取精英解种群中的路径
        res_tours = [res_indival["tour"] for res_indival in res_populations]
        in_res_tours_flag, index_res = share_distance_o2a(tour, res_tours)

        # 获取当前最佳成本
        if res_populations:
            best_res_cost = min(res_populations, key=lambda x: x["cur_cost"])["cur_cost"]
        else:
            best_res_cost = float('inf')

        # 检查是否为TSPLIB实例
        is_tsplib = False
        if "func_name" in evo_individual and is_tsplib_instance(evo_individual["func_name"]):
            is_tsplib = True

        # 设置相似度阈值和成本比例参数
        similarity_threshold = 0.7  # 相似度阈值，可根据需要调整
        cost_ratio = 0.05  # 成本比例，即x值，可根据需要调整

        # 更新种群
        if not in_tours_flag:  # 如果路径不在进化种群中
            if is_tsplib:  # 对TSPLIB实例使用新的加入条件
                # 检查是否满足成本条件：当前成本小于等于(1+x)*最佳成本
                cost_condition = cur_cost <= best_res_cost * (1 + cost_ratio) if res_populations else True

                # 检查是否满足相似度条件：与所有精英解的相似度都小于等于y
                similarity_condition = True
                for res_tour in res_tours:
                    similarity = calculate_path_similarity(tour, res_tour)
                    if similarity > similarity_threshold:
                        similarity_condition = False
                        break

                # 同时满足成本条件和相似度条件时加入精英解
                if cost_condition and similarity_condition and not in_res_tours_flag:
                    res_populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到精英解种群(TSPLIB实例)，成本: {cur_cost}，成本比: {cur_cost/best_res_cost if best_res_cost > 0 else 'N/A'}")
                else:
                    # 否则添加到进化种群
                    populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到进化种群，成本: {cur_cost}")
            else:  # 非TSPLIB实例使用原有逻辑
                # 如果成本低于精英解且不在精英解中，添加到精英解
                if (cur_cost <= best_res_cost) and not in_res_tours_flag:
                    res_populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到精英解种群，成本: {cur_cost}")
                else:
                    # 否则添加到进化种群
                    populations.append(copy.deepcopy(evo_individual))
                    self.logger.info(f"添加新路径到进化种群，成本: {cur_cost}")
        else:  # 如果路径已在进化种群中
            if is_tsplib:  # 对TSPLIB实例使用新的加入条件
                if not in_res_tours_flag:
                    # 检查是否满足成本条件
                    cost_condition = cur_cost <= best_res_cost * (1 + cost_ratio) if res_populations else True

                    # 检查是否满足相似度条件
                    similarity_condition = True
                    for res_tour in res_tours:
                        similarity = calculate_path_similarity(tour, res_tour)
                        if similarity > similarity_threshold:
                            similarity_condition = False
                            break

                    # 同时满足成本条件和相似度条件时加入精英解
                    if cost_condition and similarity_condition:
                        res_populations.append(copy.deepcopy(populations[index]))
                        self.logger.info(f"将进化种群中的路径移动到精英解种群(TSPLIB实例)，成本: {cur_cost}，成本比: {cur_cost/best_res_cost if best_res_cost > 0 else 'N/A'}")
                # 如果已在精英解中，从进化种群中移除
                elif in_res_tours_flag:
                    populations.pop(index)
                    self.logger.info("从进化种群中移除已存在于精英解的路径")
            else:  # 非TSPLIB实例使用原有逻辑
                # 检查是否应该移动到精英解
                if (cur_cost <= best_res_cost) and not in_res_tours_flag:
                    res_populations.append(copy.deepcopy(populations[index]))
                    self.logger.info(f"将进化种群中的路径移动到精英解种群，成本: {cur_cost}")
                # 如果已在精英解中，从进化种群中移除
                elif in_res_tours_flag:
                    populations.pop(index)
                    self.logger.info("从进化种群中移除已存在于精英解的路径")

        self.logger.info(f"当前进化种群大小: {len(populations)}, 精英解种群大小: {len(res_populations)}")
