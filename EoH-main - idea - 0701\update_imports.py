#!/usr/bin/env python3
"""
更新导入语句脚本 - 批量更新所有Python文件中的import语句
"""

import os
import re

def update_imports_in_file(file_path):
    """更新单个文件中的导入语句"""
    
    # 定义导入映射规则
    import_mappings = {
        # 配置模块
        r'from config import': 'from config.config import',
        r'from log_config import': 'from config.log_config import',
        
        # 专家模块
        r'from expert_base import': 'from experts.base.expert_base import',
        r'from collaboration_manager import': 'from experts.management.collaboration_manager import',
        r'from exploration_expert import': 'from experts.evolution.exploration_expert import',
        r'from exploitation_expert import': 'from experts.evolution.exploitation_expert import',
        r'from assessment_expert import': 'from experts.evolution.assessment_expert import',
        r'from landscape_expert import': 'from experts.analysis.landscape_expert import',
        r'from strategy_expert import': 'from experts.strategy.strategy_expert import',
        r'from stats_expert import': 'from experts.analysis.stats_expert import',
        r'from path_expert import': 'from experts.analysis.path_expert import',
        r'from elite_expert import': 'from experts.analysis.elite_expert import',
        r'from experts_prompt import': 'from experts.prompts.experts_prompt import',
        
        # API模块
        r'from api_general import': 'from api.api_general import',
        
        # 核心算法模块
        r'from gls_evol_enhanced import': 'from core.algorithms.gls_evol_enhanced import',
        r'from gls_operators import': 'from core.algorithms.gls_operators import',
        r'from gls_run import': 'from core.algorithms.gls_run import',
        r'from guided_local_search_with_similarity import': 'from core.algorithms.guided_local_search_with_similarity import',
        r'from optimized_topology_aware_perturbation import': 'from core.algorithms.optimized_topology_aware_perturbation import',
        
        # 优化模块
        r'from path_similarity_optimizer import': 'from core.optimization.path_similarity_optimizer import',
        r'from greedy_path_generator import': 'from core.optimization.greedy_path_generator import',
        r'from progress_calculator import': 'from core.optimization.progress_calculator import',
        
        # 数据模块
        r'from loadinstance import': 'from core.data.loadinstance import',
        r'from initpop import': 'from core.data.initpop import',
        
        # 知识库模块
        r'from knowledge_base import': 'from knowledge.knowledge_base import',
        r'from exploration_knowledge_base import': 'from knowledge.exploration_knowledge_base import',
        
        # 工具模块
        r'from stats_analyzer import': 'from utils.analyzers.stats_analyzer import',
        r'from path_structure_analyzer import': 'from utils.analyzers.path_structure_analyzer import',
        r'from time_tracker import': 'from utils.trackers.time_tracker import',
        r'from strategy_tracker import': 'from utils.trackers.strategy_tracker import',
        r'from idea_extractor import': 'from utils.extractors.idea_extractor import',
        r'from jit_warmup import': 'from utils.jit_warmup import',
        r'import utils': 'from utils import utils',
        
        # 简单导入语句
        r'import gls_evol_enhanced': 'from core.algorithms import gls_evol_enhanced',
        r'import gls_run': 'from core.algorithms import gls_run',
    }
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 应用导入映射
        for old_pattern, new_import in import_mappings.items():
            content = re.sub(old_pattern, new_import, content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"更新导入: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"更新文件失败 {file_path}: {e}")
        return False

def update_all_imports():
    """更新所有Python文件的导入语句"""
    
    # 需要更新的目录
    directories_to_update = [
        'src',
        'tools',
    ]
    
    updated_files = 0
    total_files = 0
    
    for directory in directories_to_update:
        if os.path.exists(directory):
            for root, dirs, files in os.walk(directory):
                for file in files:
                    if file.endswith('.py'):
                        file_path = os.path.join(root, file)
                        total_files += 1
                        if update_imports_in_file(file_path):
                            updated_files += 1
    
    print(f"导入更新完成: 更新了 {updated_files}/{total_files} 个文件")

if __name__ == "__main__":
    update_all_imports()
