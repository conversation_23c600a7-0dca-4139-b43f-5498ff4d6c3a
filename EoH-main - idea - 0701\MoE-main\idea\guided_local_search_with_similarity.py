# 集成路径相似度优化器的引导局部搜索算法

import time
import numpy as np
import random
import copy
import gls_operators
import utils
from gls_evol_enhanced import tour_cost


# 导入辅助函数
from gls_evol_enhanced import perturb_reorder, route2tour, tour2route, tour_cost_2End,is_valid_tsp_tour_fast


def guided_local_search_with_similarity(D, N, init_tour, init_cost, end_time, ite_max, perturbation_moves, 
                               evo_individual, populations, res_populations, first_improvement=False,
                               similarity_threshold=1.0, is_tsplib=False, cost_ratio=0.1):
    """集成路径相似度优化器的引导局部搜索算法
    
    Args:
        D: 距离矩阵
        N: 邻域矩阵
        init_tour: 初始路径
        init_cost: 初始成本
        end_time: 结束时间
        ite_max: 最大迭代次数
        perturbation_moves: 扰动次数
        evo_individual: 进化个体
        evo_populations: 进化种群
        res_populations: 精英解种群
        first_improvement: 是否使用首次改进策略
        similarity_threshold: 相似度阈值，默认为1.0（完全相同）
        is_tsplib: 是否为TSPLIB实例
        cost_ratio: 成本比例，默认为0.05
        
    Returns:
        best_route: 最优路径
        best_cost: 最优成本
    """
    # 导入时间跟踪器
    from time_tracker import time_tracker
    
    # 记录开始时间，用于监控总执行时间
    gls_start_time = time.time()
    time_tracker.start_component_timer('guided_local_search_total')
    res_tours=None
    # 初始化路径相似度优化器
    
    # gls_operators.reset_evaluation_count()
    try:
        # 使用统一的路径相似度优化器
        from path_similarity_optimizer import PathSimilarityOptimizer
        print("使用统一的路径相似度优化器")
        # 统一版本支持max_cache_size和JIT优化
        path_optimizer_res = PathSimilarityOptimizer(
            similarity_threshold=similarity_threshold,
            max_cache_size=500,
            use_jit=True
        )
        
        # 批量添加精英解到路径优化器中
        if res_populations:
            # 提取所有有效的tour
            res_tours = [res["tour"] for res in res_populations if "tour" in res]
            
            # 批量添加路径
            for tour in res_tours:
                path_optimizer_res.add_path(tour)

        print(f"路径相似度优化器已初始化，相似度阈值：{similarity_threshold}")
    except ImportError:
        print("警告：无法导入PathSimilarityOptimizer，将使用原始相似度计算方法")
        path_optimizer_res = None
    
    cur_route, cur_cost = init_tour, init_cost
    
    print(f"开始执行引导局部搜索，时间限制：{end_time - gls_start_time:.2f}秒")
    
    # 记录各个阶段的时间
    phase_times = {
        'two_opt': 0.0,
        'relocate': 0.0,
        'perturbation': 0.0,
        'population_update': 0.0,
        'similarity_check': 0.0  # 新增相似度检查时间统计
    }
    
    improved = True
    iter = 0
    while iter < int(ite_max) and time.time() < end_time:

        while improved and time.time() < end_time:

            improved = False
            
            # 记录two_opt开始时间
            two_opt_start = time.time()
            time_tracker.start_component_timer('two_opt')
            delta, new_tour = gls_operators.two_opt_a2a(cur_route, D, N, first_improvement)  # 2_opt局部搜索算子改进解
            if delta < 0 or (delta==0 and not np.array_equal(new_tour, cur_route)):
                improved = True
                cur_cost = tour_cost_2End(D, new_tour)
                cur_route = new_tour
            # 累计two_opt时间
            elapsed_time = time.time() - two_opt_start
            phase_times['two_opt'] += elapsed_time
            time_tracker.end_component_timer('two_opt')
            
            # 记录relocate开始时间
            relocate_start = time.time()
            time_tracker.start_component_timer('relocate')
            delta, new_tour = gls_operators.relocate_a2a(cur_route, D, N, first_improvement)  # 重定位局部搜索算子改进解
            if delta < 0 or (delta==0 and not np.array_equal(new_tour, cur_route)):
                improved = True
                cur_cost = tour_cost_2End(D, new_tour)
                cur_route = new_tour
            # 累计relocate时间
            elapsed_time = time.time() - relocate_start
            phase_times['relocate'] += elapsed_time
            time_tracker.end_component_timer('relocate')
                
       

        # 记录种群更新开始时间
        pop_update_start = time.time()
        time_tracker.start_component_timer('population_update')
        
        # 转换路径格式并预先计算一些值以避免重复计算
        tour = route2tour(cur_route)
        if is_valid_tsp_tour_fast(tour,len(D[0])):
            cur_cost=tour_cost(D, tour)
            best_cost = cur_cost
        else:
            print("无效的TSP路径")
            break
        # 记录相似度检查开始时间
        similarity_check_start = time.time()
        
        # 优化：只在需要时获取精英解列表，避免不必要的列表推导
        is_similar_to_res = False
        
        # 使用路径相似度优化器检查是否与精英解相似
        # 优化：提前检查路径优化器是否存在和精英解是否为空，避免不必要的计算
        if not res_populations:
            evo_individual["cur_cost"] = cur_cost
            evo_individual["tour"] = tour
            res_populations.append(copy.deepcopy(evo_individual))
            # 确保path_optimizer_res不为None再调用add_path
            if path_optimizer_res is not None:
                path_optimizer_res.add_path(copy.deepcopy(tour))
            # res_tours=[tour]
            print("res_populations为空，将当前解加入精英解种群") 
            continue
      
        if path_optimizer_res is not None and res_populations:
            # 优化：直接使用优化器的check_similarity方法，该方法内部已经实现了高效的相似度计算和缓存
            is_similar_to_res, similar_res_id, max_similarity = path_optimizer_res.check_similarity(tour)
            # print("使用优化版相似度计算方法,path_optimizer_res is not None and res_populations")
        # elif res_populations:  # 如果没有路径优化器但有精英解
        #     # 优化：只在需要时获取精英解列表
        #     res_tours = [res_indival["tour"] for res_indival in res_populations]
        #     # 使用原始方法
        #     in_res_tours_flag, _ = share_distance_o2a(tour, res_tours)
        #     is_similar_to_res = in_res_tours_flag
        #     print("使用原始相似度计算方法")
        # else:
        #     pass

        #  # 初始化相似度检查结果变量
        # is_similar_to_res = False
        # similar_res_id = -1
        # max_similarity = 0.0
        
        # # 确保res_populations不为空且有效
        # if res_populations and len(res_populations) > 0:
        #     if path_optimizer_res is not None:
        #         # 优化：直接使用优化器的check_similarity方法，该方法内部已经实现了高效的相似度计算和缓存
        #         is_similar_to_res, similar_res_id, max_similarity = path_optimizer_res.check_similarity(tour)
        #     else:  # 如果没有路径优化器但有精英解
        #         # 优化：只在需要时获取精英解列表
        #         # 确保res_tours已初始化，避免重复初始化
        #         if not res_tours or len(res_tours) == 0:
        #             res_tours = [res_indival["tour"] for res_indival in res_populations if "tour" in res_indival]
        #         # 使用原始方法，确保res_tours不为空
        #         if res_tours and len(res_tours) > 0:
        #             in_res_tours_flag, _ = share_distance_o2a(tour, res_tours)
        #             is_similar_to_res = in_res_tours_flag
        # else:
        #     evo_individual["cur_cost"] = cur_cost
        #     evo_individual["tour"] = tour
        #     res_populations.append(evo_individual)
        #     path_optimizer_res.add_path(tour)
        #     continue
        
        
        # 累计相似度检查时间
        elapsed_time = time.time() - similarity_check_start
        phase_times['similarity_check'] += elapsed_time
        
        # 优化：使用条件表达式简化代码，避免重复计算
        best_res_cost = min(res_populations, key=lambda x: x["cur_cost"])["cur_cost"] if res_populations else best_cost
        
        # 更新当前个体信息 - 只需要更新一次
        
        evo_individual["tour"] = route2tour(cur_route).astype(np.int64)
        evo_individual["cur_cost"] = tour_cost(D, evo_individual["tour"])
        
        # 优化：使用布尔变量和条件表达式简化逻辑判断
        # 根据是否为TSPLIB实例使用不同的加入条件
        if is_tsplib:
            # 检查是否满足成本条件：当前成本小于等于(1+x)*最佳成本
            cost_condition = cur_cost <= best_res_cost * (1 + cost_ratio) if res_populations else True
            # 检查是否满足相似度条件：与所有精英解的相似度都小于阈值
            similarity_condition = not is_similar_to_res
            
            # 优化：合并条件判断，减少代码分支
            should_add_to_res =(cur_cost < best_res_cost) or (cost_condition and similarity_condition)  
            
            if should_add_to_res:
                # 优化：只在确定要添加时才进行深拷贝，避免不必要的内存操作
                res_populations.append(copy.deepcopy(evo_individual))
                # 将新加入的路径添加到路径优化器
                if path_optimizer_res is not None:
                    path_optimizer_res.add_path(copy.deepcopy(tour))
        else:
            # 非TSPLIB实例使用原有逻辑，但使用路径相似度优化器检查相似性
            # 优化：合并条件判断，减少代码分支
            if cur_cost <= best_res_cost and not is_similar_to_res:
                # if cur_cost < best_res_cost:
                #     res_populations=[]
                res_populations.append(copy.deepcopy(evo_individual))
                # 将新加入的路径添加到路径优化器
                if path_optimizer_res is not None:
                    path_optimizer_res.add_path(copy.deepcopy(tour))
        
        # 累计种群更新时间
        elapsed_time = time.time() - pop_update_start
        phase_times['population_update'] += elapsed_time
        time_tracker.end_component_timer('population_update')

        # 更新最佳路径和成本
        best_path = route2tour(cur_route)
        best_cost = tour_cost(D, best_path)
        
        # 根据问题规模和剩余时间动态调整扰动次数
        n = len(best_path)
        remaining_time = end_time - time.time()
        
        if remaining_time <= 0:
            break  # 如果已经没有时间了，直接退出
            
        # 根据问题规模和剩余时间调整扰动次数
        if n > 50:
            # 大规模问题
            if remaining_time < 30:
                actual_perturbation_moves = 1  # 时间不足时只做一次扰动
            else:
                actual_perturbation_moves = min(perturbation_moves, 3)  # 最多3次扰动
        elif n > 30:
            # 中等规模问题
            actual_perturbation_moves = min(perturbation_moves, 5)
        else:
            # 小规模问题
            actual_perturbation_moves = perturbation_moves
        
        for p_move in range(actual_perturbation_moves):   
            # 检查是否超时 - 每次扰动前检查
            if time.time() >= end_time:
                break
            
            # 记录扰动开始时间
            perturb_start = time.time()
            time_tracker.start_component_timer('perturbation')
            
            # 再次检查是否超时 - 确保在调用耗时函数前检查
            if time.time() >= end_time:
                time_tracker.end_component_timer('perturbation')
                break
                
            # 使用增强版扰动函数，传入距离矩阵、迭代信息、已知解和结束时间
            new_path = perturb_reorder(
                path=best_path, 
                dis_m=D, 
                iteration=iter, 
                max_iterations=1000,
                strategy='topology_aware',  # 使用默认的拓扑感知策略
                known_solutions=res_tours,  # 传入已知的最优解
                end_time=end_time  # 传递结束时间
            )
            
            # 优化：扰动操作后智能清理缓存，根据内存使用情况动态调整
            try:
                from optimized_topology_aware_perturbation import limit_cache_size, segment_cache
                
                # 根据问题规模和迭代次数动态调整缓存清理策略
                dynamic_cache_size = 1000
                if n > 100:  # 大规模问题
                    dynamic_cache_size = 800
                    cleanup_probability = 0.15  # 增加清理概率
                elif n < 50:  # 小规模问题
                    dynamic_cache_size = 1200
                    cleanup_probability = 0.05  # 减少清理概率
                else:  # 中等规模问题
                    cleanup_probability = 0.1
                
                # 根据迭代次数调整清理概率，迭代次数越多，清理概率越高
                if iter > ite_max * 0.7:
                    cleanup_probability *= 1.5
                
                # 根据缓存大小和清理概率决定是否清理
                cache_usage_ratio = len(segment_cache) / dynamic_cache_size if dynamic_cache_size > 0 else 0
                if random.random() < cleanup_probability * cache_usage_ratio:
                    force_cleanup = cache_usage_ratio > 0.8
                    limit_cache_size(max_size=dynamic_cache_size, force_cleanup=force_cleanup)
            except (ImportError, AttributeError):
                # 如果导入失败或属性不存在，说明没有使用优化的拓扑感知扰动
                pass
            except Exception as e:
                print(f"缓存清理失败: {str(e)}")
                # 继续执行，不影响主流程
                
            # 累计扰动时间
            elapsed_time = time.time() - perturb_start
            phase_times['perturbation'] += elapsed_time
            time_tracker.end_component_timer('perturbation')
            
            # 优化：避免不必要的类型转换，只在必要时进行转换
            # 检查是否已经是numpy数组且类型为int
            if isinstance(new_path, np.ndarray) and new_path.dtype == np.int64:
                cur_route = tour2route(new_path)
            else:
                cur_route = np.array(tour2route(new_path), dtype=np.int64)
            
            # 确保D和N是整数类型的numpy数组，避免重复转换
            if not isinstance(D, np.ndarray) or D.dtype != np.int64:
                D = np.array(D, dtype=np.int64)
            if not isinstance(N, np.ndarray) or N.dtype != np.int64:
                N = np.array(N, dtype=np.int64)
            
            # 记录two_opt开始时间
            two_opt_start = time.time()
            time_tracker.start_component_timer('two_opt')
            delta, new_tour = gls_operators.two_opt_a2a(cur_route, D, N, first_improvement)  # 2_opt局部搜索算子改进解
            if delta < 0 or (delta==0 and not np.array_equal(new_tour, cur_route)):
                improved = True
                cur_cost = tour_cost_2End(D, new_tour)
                cur_route = new_tour
            # 累计two_opt时间
            elapsed_time = time.time() - two_opt_start
            phase_times['two_opt'] += elapsed_time
            time_tracker.end_component_timer('two_opt')
        
        iter += 1
    
    # 输出各阶段时间占比
    total_time = sum(phase_times.values())
    if total_time > 0:
        print("\n各阶段时间占比:")
        # 按时间占比从大到小排序输出
        sorted_phases = sorted(phase_times.items(), key=lambda x: x[1], reverse=True)
        for phase, time_spent in sorted_phases:
            percentage = time_spent / total_time * 100
            print(f"  - {phase}: {percentage:.2f}% ({time_spent:.3f}秒)")
    
    # 输出路径相似度优化器统计信息
    if path_optimizer_res is not None:
        stats = path_optimizer_res.get_statistics()
        print("\n路径相似度优化器统计信息:")
        print(f"  - 路径数量: {stats['path_count']}")
        print(f"  - 相似度计算次数: {stats['similarity_calculations']}")
        print(f"  - 缓存命中率: {stats['cache_hit_rate']:.2f}")
        print(f"  - 缓存大小: {stats.get('cache_size', 0)}")
        print(f"  - 相似度阈值: {path_optimizer_res.similarity_threshold}")
        
        # 计算相似度检查效率
        if stats['similarity_calculations'] > 0 and phase_times['similarity_check'] > 0:
            efficiency = stats['similarity_calculations'] / phase_times['similarity_check']
            print(f"  - 相似度计算效率: {efficiency:.2f}次/秒")
    # print("评估次数：", gls_operators.get_evaluation_count()/len(init_tour))
    time_tracker.end_component_timer('guided_local_search_total')
    return cur_route, cur_cost

# 示例用法
if __name__ == "__main__":
    print("集成路径相似度优化器的引导局部搜索算法")
    print("请在主程序中调用此函数")