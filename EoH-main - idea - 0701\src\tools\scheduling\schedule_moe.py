 #!/usr/bin/env python3
"""
Schedule the execution of `moe_main.py` for a user-specified time and pass through
any desired command-line arguments.

Examples
--------
1. Run tomorrow at 01:30 with default parameters::

    python schedule_moe.py --time "01:30"

2. Run at a concrete timestamp and pass additional options to `moe_main.py`::

    python schedule_moe.py --time "2025-06-18 15:45" -- --func_begin 26 --func_end 30 --iter_num 5

Notes
-----
* Time strings accepted:
    * "HH:MM" or "HH:MM:SS" (today if still in the future, otherwise tomorrow)
    * "YYYY-MM-DD HH:MM" (seconds optional)
* All arguments after the optional `--` separator are forwarded verbatim to
  `moe_main.py`.
"""
from __future__ import annotations

import argparse
import datetime as _dt
import os
import subprocess
import sys
import time
from typing import List

# --------------------------------------------------------------------------------------
# Helpers
# --------------------------------------------------------------------------------------


def _parse_target_dt(ts: str) -> _dt.datetime:
    """Parse *ts* into a :class:`datetime.datetime`.

    Accepted formats:
    * ``YYYY-MM-DD HH:MM[:SS]``
    * ``HH:MM[:SS]`` (interpreted as today or tomorrow if the time has passed)
    """
    ts = ts.strip()
    date_fmt_variants = [
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d %H:%M",
    ]
    time_fmt_variants = [
        "%H:%M:%S",
        "%H:%M",
    ]

    now = _dt.datetime.now()

    # full date specified
    for fmt in date_fmt_variants:
        try:
            dt = _dt.datetime.strptime(ts, fmt)
            return dt
        except ValueError:
            pass

    # only time specified ‑> today/tomorrow logic
    for fmt in time_fmt_variants:
        try:
            t = _dt.datetime.strptime(ts, fmt).time()
            candidate = now.replace(hour=t.hour, minute=t.minute, second=t.second,
                                     microsecond=0)
            if candidate <= now:
                # schedule for the next day
                candidate += _dt.timedelta(days=1)
            return candidate
        except ValueError:
            pass

    raise argparse.ArgumentTypeError(
        f"Unrecognised time format: '{ts}'. See module docstring for examples.")


# --------------------------------------------------------------------------------------
# CLI parsing
# --------------------------------------------------------------------------------------

def _build_parser() -> argparse.ArgumentParser:
    p = argparse.ArgumentParser(
        description="Schedule the execution of moe_main.py at a specified time.",
        formatter_class=argparse.RawTextHelpFormatter,
        add_help=False,
    )
    group = p.add_argument_group("Required")
    group.add_argument("--time", required=True, type=_parse_target_dt,
                       help="Target start time (see docstring for formats).")

    p.add_argument("--python", default=sys.executable,
                   help="Python interpreter to use (default: current).")

    p.add_argument("-h", "--help", action="help",
                   help="Show this help message and exit.")

    # Everything after "--" gets forwarded to moe_main.py unchanged.
    p.add_argument("moe_args", nargs=argparse.REMAINDER,
                   help="Arguments forwarded to moe_main.py (must be after '--').")
    return p


# --------------------------------------------------------------------------------------
# Main logic
# --------------------------------------------------------------------------------------

def _sleep_until(target: _dt.datetime) -> None:
    now = _dt.datetime.now()
    remaining = (target - now).total_seconds()
    if remaining <= 0:
        return  # should not happen thanks to parser but be safe

    print(f"Current time: {now.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Scheduled run at: {target.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Sleeping for {int(remaining)} seconds (~{remaining/60:.1f} minutes)...")
    try:
        time.sleep(remaining)
    except KeyboardInterrupt:
        print("\nInterrupted while waiting. Exiting.")
        sys.exit(130)


def main(argv: List[str] | None = None) -> None:
    parser = _build_parser()
    args = parser.parse_args(argv)

    # Compute path to moe_main.py (assumed to be in the same directory as this script)
    script_dir = os.path.dirname(os.path.abspath(__file__))
    moe_main_path = os.path.join(script_dir, "moe_main.py")
    if not os.path.isfile(moe_main_path):
        parser.error(f"moe_main.py not found at expected location: {moe_main_path}")

    # Wait until target time
    _sleep_until(args.time)

    # Build command
    cmd = [args.python, moe_main_path] + args.moe_args

    print("\n=== Launching moe_main.py ===")
    print("Command:", " ".join(cmd))
    print("============================\n")

    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as exc:
        print(f"moe_main.py exited with non-zero status {exc.returncode}")
        sys.exit(exc.returncode)


if __name__ == "__main__":
    main()
