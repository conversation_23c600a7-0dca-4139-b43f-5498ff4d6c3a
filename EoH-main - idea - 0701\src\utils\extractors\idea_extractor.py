import logging
from .. import ResponseParser

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

class IdeaExtractor:
    """从LLM响应中提取想法和路径的工具类"""

    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.parser = ResponseParser(self.logger)
    
    def extract_exploration_path(self, response, original_tour=None):
        """从探索响应中提取路径

        参数:
            response (str): LLM的响应文本
            original_tour (list): 原始路径，用于回退

        返回:
            dict: 包含提取的路径和其他信息
        """
        self.logger.info("开始从探索响应中提取路径")
        return self.parser.extract_path_from_response(response, original_tour)

    def extract_exploitation_path(self, response, original_tour=None):
        """从利用响应中提取路径

        参数:
            response (str): LLM的响应文本
            original_tour (list): 原始路径，用于回退

        返回:
            dict: 包含提取的路径和其他信息
        """
        self.logger.info("开始从利用响应中提取路径")
        return self.parser.extract_path_from_response(response, original_tour)
    
    def extract_ideas(self, response):
        """从响应中提取想法
        
        参数:
            response (str): LLM的响应文本
            
        返回:
            list: 提取的想法列表
        """
        self.logger.info("开始从响应中提取想法")
        
        try:
            # 尝试提取JSON格式的想法
            json_match = re.search(r'```json\s*({[\s\S]*?})\s*```', response)
            if json_match:
                ideas_data = json.loads(json_match.group(1))
                if "ideas" in ideas_data:
                    self.logger.info(f"成功从JSON中提取想法: {len(ideas_data['ideas'])}个")
                    return ideas_data["ideas"]
            
            # 尝试提取列表格式的想法
            ideas = []
            idea_matches = re.findall(r'\d+\.\s*([^\n]+)', response)
            if idea_matches:
                ideas = idea_matches
                self.logger.info(f"成功提取列表格式的想法: {len(ideas)}个")
                return ideas
            
            # 如果无法提取，返回空列表
            self.logger.warning("无法从响应中提取想法，返回空列表")
            return []
            
        except Exception as e:
            self.logger.error(f"提取想法时出错: {str(e)}")
            return []