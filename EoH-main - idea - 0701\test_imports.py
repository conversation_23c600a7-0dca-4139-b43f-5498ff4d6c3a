#!/usr/bin/env python3
"""
测试导入语句 - 验证重构后的导入是否正常工作
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_imports():
    """测试各个模块的导入"""
    
    test_results = []
    
    # 测试配置模块
    try:
        from config.config import ALGORITHM_CONFIG, GLOBAL_INPUT_PATH, GLOBAL_OUTPUT_PATH
        test_results.append(("config.config", "SUCCESS"))
    except Exception as e:
        test_results.append(("config.config", f"FAILED: {e}"))
    
    try:
        from config.log_config import setup_logging
        test_results.append(("config.log_config", "SUCCESS"))
    except Exception as e:
        test_results.append(("config.log_config", f"FAILED: {e}"))
    
    # 测试专家基础模块
    try:
        from experts.base.expert_base import ExpertBase
        test_results.append(("experts.base.expert_base", "SUCCESS"))
    except Exception as e:
        test_results.append(("experts.base.expert_base", f"FAILED: {e}"))
    
    # 测试专家管理模块
    try:
        from experts.management.collaboration_manager import ExpertCollaborationManager
        test_results.append(("experts.management.collaboration_manager", "SUCCESS"))
    except Exception as e:
        test_results.append(("experts.management.collaboration_manager", f"FAILED: {e}"))
    
    # 测试进化专家
    try:
        from experts.evolution.exploration_expert import ExplorationExpert
        test_results.append(("experts.evolution.exploration_expert", "SUCCESS"))
    except Exception as e:
        test_results.append(("experts.evolution.exploration_expert", f"FAILED: {e}"))
    
    try:
        from experts.evolution.exploitation_expert import ExploitationExpert
        test_results.append(("experts.evolution.exploitation_expert", "SUCCESS"))
    except Exception as e:
        test_results.append(("experts.evolution.exploitation_expert", f"FAILED: {e}"))
    
    # 测试分析专家
    try:
        from experts.analysis.landscape_expert import LandscapeExpert
        test_results.append(("experts.analysis.landscape_expert", "SUCCESS"))
    except Exception as e:
        test_results.append(("experts.analysis.landscape_expert", f"FAILED: {e}"))
    
    # 测试核心算法
    try:
        from core.algorithms.gls_evol_enhanced import tour_cost
        test_results.append(("core.algorithms.gls_evol_enhanced", "SUCCESS"))
    except Exception as e:
        test_results.append(("core.algorithms.gls_evol_enhanced", f"FAILED: {e}"))
    
    # 测试数据模块
    try:
        from core.data.loadinstance import load_all_instances
        test_results.append(("core.data.loadinstance", "SUCCESS"))
    except Exception as e:
        test_results.append(("core.data.loadinstance", f"FAILED: {e}"))
    
    # 测试API模块
    try:
        from api.api_general import InterfaceAPI
        test_results.append(("api.api_general", "SUCCESS"))
    except Exception as e:
        test_results.append(("api.api_general", f"FAILED: {e}"))
    
    # 测试工具模块
    try:
        from utils import utils
        test_results.append(("utils.utils", "SUCCESS"))
    except Exception as e:
        test_results.append(("utils.utils", f"FAILED: {e}"))
    
    # 输出测试结果
    print("=" * 60)
    print("导入测试结果:")
    print("=" * 60)
    
    success_count = 0
    for module, result in test_results:
        status = "✓" if result == "SUCCESS" else "✗"
        print(f"{status} {module:<40} {result}")
        if result == "SUCCESS":
            success_count += 1
    
    print("=" * 60)
    print(f"测试完成: {success_count}/{len(test_results)} 个模块导入成功")
    
    return success_count == len(test_results)

if __name__ == "__main__":
    success = test_imports()
    if success:
        print("\n🎉 所有导入测试通过！项目重构成功！")
    else:
        print("\n❌ 部分导入测试失败，需要进一步修复。")
    
    sys.exit(0 if success else 1)
