# EoH-TSP-Solver 项目结构指南

## 概述

本文档描述了EoH-TSP-Solver项目重构后的目录结构和使用方法。

## 项目启动

### 运行主程序

```bash
cd src
python main.py --iter_num 2 --pop_size 5
```

### 参数说明

- `--func_begin`: 起始索引，默认为0
- `--func_end`: 结束索引，默认为0  
- `--iter_num`: 迭代次数，默认为2
- `--pop_size`: 种群规模，默认为5

## 目录结构说明

### src/ - 源代码目录

#### core/ - 核心功能模块
- `algorithms/`: TSP算法实现
  - 包含GLS、2-opt、relocate等核心算法
- `optimization/`: 优化模块
  - 路径相似度优化、贪心路径生成等
- `data/`: 数据处理模块
  - 实例加载、种群初始化等

#### experts/ - 专家系统模块
- `base/`: 专家基础类
- `analysis/`: 分析专家（景观、统计、路径、精英）
- `evolution/`: 进化专家（探索、利用、评估）
- `strategy/`: 策略专家
- `management/`: 协作管理
- `prompts/`: 提示词模块

#### 其他模块
- `api/`: LLM API接口
- `knowledge/`: 知识库系统
- `utils/`: 工具模块
- `config/`: 配置模块

### data/ - 数据目录
- `benchmark/MMTSP/`: 基准测试实例
- `knowledge_base/`: 专家学习数据

### src/tools/ - 工具脚本
- `analysis/`: 分析工具
- `visualization/`: 可视化工具
- `testing/`: 测试工具
- `scheduling/`: 调度工具

## 配置说明

### API配置
在 `src/config/config.py` 中配置LLM API密钥：
- 讯飞星火API
- DeepSeek API
- Gemini API
- 智谱AI API

### 路径配置
项目会自动检测路径，主要路径包括：
- 输入路径: `data/benchmark/MMTSP/`
- 输出路径: `data/benchmark/MMTSP/instance_pkl/`
- 日志路径: `results/deepseekR1-test/Log/`

## 开发指南

### 添加新专家
1. 在相应的experts子目录中创建新文件
2. 继承ExpertBase基类
3. 在collaboration_manager.py中注册新专家

### 添加新算法
1. 在core/algorithms/目录中添加算法文件
2. 在相关专家中导入和使用新算法

### 添加新工具
1. 在src/tools/相应子目录中添加工具脚本
2. 确保工具脚本可独立运行

## 测试

### 运行基本测试
```bash
python simple_test.py
```

### 运行特定测试
```bash
python src/tools/testing/test_refactoring.py
```

## 注意事项

1. **API密钥安全**: 不要将API密钥提交到版本控制系统
2. **路径分隔符**: 项目支持Windows和Linux路径格式
3. **依赖管理**: 确保安装了所需的Python包
4. **内存使用**: 大规模实例可能需要较多内存

## 故障排除

### 导入错误
如果遇到导入错误，检查：
1. Python路径是否正确设置
2. __init__.py文件是否存在
3. 模块名称是否正确

### 路径错误
如果遇到路径错误，检查：
1. 数据文件是否存在于正确位置
2. 配置文件中的路径设置是否正确
3. 工作目录是否正确

### API错误
如果遇到API错误，检查：
1. API密钥是否正确配置
2. 网络连接是否正常
3. API服务是否可用
