def normalize_tsp_path(path):
    """
    标准化TSP路径，移除起点和终点，并确保路径从最小节点开始
    
    Args:
        path: 包含节点序列的列表或字符串
        
    Returns:
        标准化后的路径字符串，用于比较
    """
    # 如果输入是字符串，将其转换为列表
    if isinstance(path, str):
        # 分割字符串并转换为整数列表
        path = [int(x) for x in path.strip().split()]
    
    # 移除第一个数字（成本值）
    if len(path) > 0 and path[0] > 1000:  # 假设成本值大于1000
        path = path[1:]
    
    # 移除起点和终点（通常是0）
    if 0 in path:
        # 找到所有0的位置
        zero_indices = [i for i, x in enumerate(path) if x == 0]
        # 如果有多个0，只保留路径中的节点
        if len(zero_indices) > 0:
            # 移除起点和终点的0
            if zero_indices[0] == 0:  # 如果0在开头
                path = path[1:]
            if len(path) > 0 and path[-1] == 0:  # 如果0在结尾
                path = path[:-1]
    
    # 如果路径为空，返回空字符串
    if not path:
        return ""
    
    # 找到最小值的索引
    min_value = min(path)
    min_index = path.index(min_value)
    
    # 重新排列路径，使其从最小值开始
    normalized_path = path[min_index:] + path[:min_index]
    
    # 将路径转换为字符串以便比较
    return " ".join(map(str, normalized_path))

def find_duplicate_paths(file_path):
    """
    分析TSP解决方案文件，查找重复的路径
    
    Args:
        file_path: 解决方案文件的路径
        
    Returns:
        包含重复路径信息的字典
    """
    paths = []
    normalized_paths = {}
    duplicates = {}
    
    # 读取文件
    with open(file_path, 'r') as f:
        lines = f.readlines()
    
    # 处理每一行
    for i, line in enumerate(lines):
        if line.strip():  # 跳过空行
            # 标准化路径
            normalized = normalize_tsp_path(line)
            
            # 存储原始路径和标准化路径
            paths.append(line.strip())
            
            # 检查是否已存在相同的标准化路径
            if normalized in normalized_paths:
                # 如果已存在，添加到重复列表
                if normalized not in duplicates:
                    duplicates[normalized] = [normalized_paths[normalized]]
                duplicates[normalized].append(i)
            else:
                # 如果不存在，添加到标准化路径字典
                normalized_paths[normalized] = i
    
    return {
        "total_paths": len(paths),
        "unique_paths": len(normalized_paths),
        "duplicate_groups": len(duplicates),
        "duplicates": duplicates,
        "paths": paths
    }

def print_duplicate_info(result):
    """
    打印重复路径的信息
    
    Args:
        result: find_duplicate_paths函数的返回结果
    """
    print(f"总路径数: {result['total_paths']}")
    print(f"唯一路径数: {result['unique_paths']}")
    print(f"重复路径组数: {result['duplicate_groups']}")
    
    if result['duplicate_groups'] > 0:
        print("\n重复路径详情:")
        for normalized, indices in result['duplicates'].items():
            print(f"\n重复路径组 (标准化后): {normalized}")
            print("原始路径:")
            for idx in indices:
                print(f"  行 {idx+1}: {result['paths'][idx]}")
    else:
        print("\n没有发现重复路径。")

# 主函数
def main():
    file_path = "c:\\Users\\<USER>\\Desktop\\EoH-main - idea - 0312-feedback\\EoH-main\\results\\solutions\\composite13_66_20250406_165643.solution"
    result = find_duplicate_paths(file_path)
    print_duplicate_info(result)

if __name__ == "__main__":
    main()