"""
策略选择专家模块

包含StrategyExpert类，负责为每个个体分配最适合的策略。
"""

from expert_base import ExpertBase
from experts_prompt import STRATEGY_PROMPT, generate_strategy_expert_prompt, parse_expert_response


class StrategyExpert(ExpertBase):
    """策略选择专家，为每个个体分配最适合的策略"""
    
    def __init__(self, interface_llm):
        super().__init__()
        self.interface_llm = interface_llm
        # Use the optimized strategy prompt template from experts_prompt.py
        self.prompt_template = STRATEGY_PROMPT
    
    def analyze(self, landscape_report, populations, iteration, strategy_feedback=None):
        """基于景观分析，为每个个体分配策略"""
        self.logger.info("开始策略分配分析")
        
        # Generate the optimized prompt
        prompt = generate_strategy_expert_prompt(
            landscape_report=landscape_report,
            population=populations,
            previous_feedback=strategy_feedback,
            iteration=iteration
        )
        
        # 调用LLM获取策略分配结果
        self.logger.info(f"发送给LLM的策略分配提示词: {prompt}")
        self.logger.info("调用LLM进行策略分配")
        strategy_response = self.interface_llm.get_response(prompt)
        self.logger.info(f"LLM返回的策略分配结果: {strategy_response}")
        
        # 解析策略分配结果
        try:
            # Parse the response
            strategy_data = parse_expert_response(strategy_response)
            
            if "error" in strategy_data:
                self.logger.warning(f"解析策略分配结果时出错: {strategy_data['error']}")
                # 使用默认策略
                strategy_selection = ["explore" if i % 2 == 0 else "exploit" for i in range(len(populations))]
            else:
                individual_assignments = strategy_data.get("individual_assignments", {})
                
                # 转换为策略列表
                strategy_selection = []
                for i in range(len(populations)):
                    strategy = individual_assignments.get(str(i), "explore")
                    strategy_selection.append(strategy)
        except Exception as e:
            self.logger.error(f"解析策略分配结果时出错: {str(e)}")
            # 使用默认策略
            strategy_selection = ["explore" if i % 2 == 0 else "exploit" for i in range(len(populations))]
        
        self.logger.info(f"策略分配完成: {strategy_selection}")
        return strategy_selection, strategy_response
    
    def generate_report(self, analysis_result):
        """生成策略分配报告"""
        strategy_selection, strategy_response = analysis_result
        return {
            "strategy_selection": strategy_selection,
            "strategy_response": strategy_response
        }
