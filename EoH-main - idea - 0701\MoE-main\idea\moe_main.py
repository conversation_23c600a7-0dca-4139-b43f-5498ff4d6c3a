"""
多专家协作进化算法框架 - 主程序

模块化重构后的主程序，只包含main函数和必要的导入。
所有专家类已分离到独立模块中。
"""

import argparse
import copy
import logging
import os
import sys

# 导入配置和工具模块
from config import ALGORITHM_CONFIG, GLOBAL_INPUT_PATH, GLOBAL_OUTPUT_PATH
from collaboration_manager import ExpertCollaborationManager
from api_general import InterfaceAPI
from loadinstance import load_all_instances
from log_config import setup_logging, get_instance_log_file
from initpop import INIT
import utils

# 获取当前目录和项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)

# 设置主日志记录器
main_logger = logging.getLogger(__name__)


def main():
    """多专家系统主函数"""
    # 导入并调用JIT预热模块，确保在处理实际数据前完成JIT编译
    from jit_warmup import warmup_jit_functions
    warmup_jit_functions()
    
    parser = argparse.ArgumentParser(description="多专家协作进化算法框架")
    parser.add_argument('--func_begin', type=int, required=False, default=0, help='起始索引，默认为0')
    parser.add_argument('--func_end', type=int, required=False, default=0, help='结束索引，默认为0')
    parser.add_argument('--iter_num', type=int, help='迭代次数', default=2)
    parser.add_argument('--pop_size', type=int, help='种群规模', default=5)
    args = parser.parse_args()

    # 定义实例列表并处理空值
    func_name = [
        "simple1_9", "simple2_10", "simple3_10", "simple4_11", "simple5_12", "simple6_12",
        "geometry1_10", "geometry2_12", "geometry3_10", "geometry4_10", "geometry5_10", "geometry6_15",
        "composite1_28", "composite2_34", "composite3_22", "composite4_33", "composite5_35", "composite6_39", 
        "composite7_42", "composite8_45", "composite9_48", "composite10_55", "composite11_59", "composite12_60", "composite13_66",
        "eil51", "berlin52", "st70", "pr76", "kroA100", "lin105"
    ]
    func_name = [name for name in func_name if name]

    # 设置func_end默认值
    if args.func_end == 0 or args.func_end >= len(func_name):
        args.func_end = args.func_begin

    # 验证参数有效性
    if args.func_begin < 0 or args.func_end >= len(func_name) or args.func_begin > args.func_end:
        print(f"错误：参数范围无效。有效范围: 0-{len(func_name)-1}，当前值: {args.func_begin}-{args.func_end}")
        sys.exit(1)
    
    # 设置日志系统
    # 修改日志保存路径到MoE-main/Log文件夹
    log_dir = os.path.join(os.path.dirname(current_dir), "Log")
    os.makedirs(log_dir, exist_ok=True)
    setup_logging(log_dir=log_dir, log_file="moe_app.log")
    
    # 打印路径信息以便调试
    main_logger.info(f"项目根目录: {project_root}")
    main_logger.info(f"输入路径: {GLOBAL_INPUT_PATH}")
    main_logger.info(f"输出路径: {GLOBAL_OUTPUT_PATH}")
    
    # 检查输入目录是否存在
    if not os.path.exists(GLOBAL_INPUT_PATH):
        print(f"错误: 输入目录不存在: {GLOBAL_INPUT_PATH}")
        return
    
    # 确保目录存在
    os.makedirs(GLOBAL_INPUT_PATH, exist_ok=True)
    os.makedirs(GLOBAL_OUTPUT_PATH, exist_ok=True)
    
    # 从配置文件获取算法参数
    pop_size = args.pop_size if args.pop_size else ALGORITHM_CONFIG.get("pop_size", 20)
    evo_num = args.iter_num if args.iter_num else ALGORITHM_CONFIG.get("evo_num", 10)
    
    # 加载实例 - 使用封装的函数直接返回instances_selected
    instances_selected = load_all_instances(func_name, GLOBAL_INPUT_PATH, args.func_begin, args.func_end, GLOBAL_OUTPUT_PATH)

    if len(instances_selected) == 0:
        print("警告: 没有成功加载任何实例")
        return
    
    # API接口设置
    debug_mode = False
    interface_llm = InterfaceAPI(api_type="gemini", debug_mode=debug_mode)
    # interface_llm = InterfaceAPI(api_type="deepseek")
    
    # 初始化专家协作管理器
    collaboration_manager = ExpertCollaborationManager(interface_llm)
    
    # 主循环，对每个实例进行处理
    for iter_idx in range(len(instances_selected)):
        instance = instances_selected[iter_idx]
        main_logger.info(f"开始处理实例: {instance['func_name']}")
        
        # 初始化种群
        # 使用INIT类的静态方法初始化种群
        populations = INIT.mixed_init(instance['distance_matrix'], pop_size)
        # 计算种群成本
        populations = INIT.calculate_population_costs(populations, instance['distance_matrix'])
        cur_best_cost = min(populations, key=lambda x: x["cur_cost"])["cur_cost"]
        print(f"当前最佳适应度：{cur_best_cost}")
        main_logger.info(f"初始化种群完成，当前最佳适应度: {cur_best_cost}")
        
        # 为当前实例创建专用的日志文件处理器
        # 确保使用MoE-main/Log文件夹保存实例日志
        instance_log_dir = os.path.join(os.path.dirname(current_dir), "Log")
        os.makedirs(instance_log_dir, exist_ok=True)
        instance_log_file = get_instance_log_file(instance['func_name'], instance_log_dir)
        file_handler = logging.FileHandler(instance_log_file, mode='w', encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        root_logger = logging.getLogger('')
        root_logger.addHandler(file_handler)
        
        # 存储各代进化个体和精英解
        res_populations = []  # 存储精英解
        strategy_feedback = None

        # 每个实例进化evo_num代
        for evo_iter in range(evo_num):
            main_logger.info(f"{instance['func_name']} 开始进化第 {evo_iter+1} 代")
            print(f"iter: {evo_iter}")

            # 1. 分析阶段
            landscape_report, old_stats_report = collaboration_manager.run_analysis_phase(
                populations=populations,
                res_populations=res_populations,
                coordinates=instance["coordinate"],
                distance_matrix=instance["distance_matrix"],
                iteration=evo_iter,
                total_iterations=evo_num
            )
            # 记录完整的景观分析报告
            main_logger.info(f"景观分析完整报告: {landscape_report}")

            # 2. 策略分配阶段
            strategy_result = collaboration_manager.run_strategy_phase(
                landscape_report=landscape_report,
                populations=populations,
                iteration=evo_iter,
                strategy_feedback=strategy_feedback
            )
            strategy_selection, strategy_response = strategy_result
            # 记录完整的策略分配报告
            main_logger.info(f"策略分配: {strategy_selection}")
            main_logger.info(f"策略分配完整报告: {strategy_response}")

            # 保存当前种群副本用于后续评估
            copy.deepcopy(populations)

            # 3. 进化阶段
            new_populations = collaboration_manager.run_evolution_phase(
                populations=populations,
                strategies=strategy_selection,
                landscape_report=landscape_report,
                distance_matrix=instance["distance_matrix"],
                res_populations=res_populations  # 传递精英解集合
            )

            # 更新种群
            populations = new_populations
            res_populations.sort(key=lambda x: x["cur_cost"])

            # 4. 评估阶段
            # 为新种群计算统计数据
            stats_expert = collaboration_manager.experts["stats"]
            new_stats_analysis = stats_expert.analyze(populations)
            new_stats_report = stats_expert.generate_report(new_stats_analysis, instance["coordinate"], instance["distance_matrix"])

            assessment_report = collaboration_manager.run_assessment_phase(
                old_stats_report, new_stats_report, strategy_selection, evo_iter, evo_num,
                old_res_populations=res_populations, new_res_populations=res_populations
            )

            # 更新下一次迭代的输入
            strategy_feedback = assessment_report
            populations = new_populations
            res_populations = res_populations

            main_logger.info(f"--- Finished Evolution Iteration {evo_iter+1} ---")

        # 保存最终结果，使用封装的保存函数
        output_base_dir = os.path.dirname(current_dir)
        utils.save_optimization_results(
            res_populations=res_populations,
            instance_name=instance['func_name'],
            output_base_dir=output_base_dir,
            logger=main_logger
        )

        main_logger.info(f"实例 {instance['func_name']} 处理完成")

        # 移除实例专用的日志处理器
        root_logger.removeHandler(file_handler)
        file_handler.close()


if __name__ == "__main__":
    main()
