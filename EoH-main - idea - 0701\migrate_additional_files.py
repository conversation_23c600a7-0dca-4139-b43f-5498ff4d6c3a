#!/usr/bin/env python3
"""
迁移其他文件 - 工具脚本、数据、文档等
"""

import os
import shutil

def migrate_additional_files():
    """迁移其他文件"""
    
    # 定义文件迁移映射
    additional_migrations = {
        # 分析工具
        "analyze_tsp_paths.py": "src/tools/analysis/analyze_tsp_paths.py",
        "plot_all_instances.py": "src/tools/analysis/plot_all_instances.py",
        "plot_solution_paths.py": "src/tools/analysis/plot_solution_paths.py",
        "plot_specific_solution.py": "src/tools/analysis/plot_specific_solution.py",

        # 可视化工具
        "enhanced_tsp_visualization.py": "src/tools/visualization/enhanced_tsp_visualization.py",

        # 测试工具
        "MoE-main/idea/test_normalize_path.py": "src/tools/testing/test_normalize_path.py",
        "MoE-main/idea/test_refactoring.py": "src/tools/testing/test_refactoring.py",
        "MoE-main/idea/update_solution_costs.py": "src/tools/testing/update_solution_costs.py",

        # 调度工具
        "MoE-main/idea/schedule_moe.py": "src/tools/scheduling/schedule_moe.py",
        
        # 文档
        "MoE-main/idea/REFACTORING_SUMMARY.md": "docs/REFACTORING_SUMMARY.md",
    }
    
    # 执行文件迁移
    for source, destination in additional_migrations.items():
        try:
            if os.path.exists(source):
                # 确保目标目录存在
                os.makedirs(os.path.dirname(destination), exist_ok=True)
                # 复制文件
                shutil.copy2(source, destination)
                print(f"迁移: {source} -> {destination}")
            else:
                print(f"源文件不存在: {source}")
        except Exception as e:
            print(f"迁移失败 {source} -> {destination}: {e}")
    
    # 迁移数据目录
    data_migrations = [
        ("benchmark_MMTSP", "data/benchmark/MMTSP"),
        ("knowledge_base", "data/knowledge_base")
    ]
    
    for source_dir, dest_dir in data_migrations:
        try:
            if os.path.exists(source_dir):
                # 如果目标目录存在，先删除
                if os.path.exists(dest_dir):
                    shutil.rmtree(dest_dir)
                # 复制整个目录
                shutil.copytree(source_dir, dest_dir)
                print(f"迁移目录: {source_dir} -> {dest_dir}")
            else:
                print(f"源目录不存在: {source_dir}")
        except Exception as e:
            print(f"迁移目录失败 {source_dir} -> {dest_dir}: {e}")

if __name__ == "__main__":
    migrate_additional_files()
    print("其他文件迁移完成!")
