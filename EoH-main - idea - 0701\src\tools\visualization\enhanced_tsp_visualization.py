import numpy as np
import matplotlib.pyplot as plt
import os
import glob
import re
from matplotlib.patches import Rectangle
from adjustText import adjust_text
from matplotlib.colors import LinearSegmentedColormap
import matplotlib.patheffects as PathEffects
import datetime

def read_tsp_file(file_path):
    """
    读取TSP文件中的城市坐标
    
    :param file_path: TSP文件路径
    :return: 城市坐标的numpy数组，形状为(n, 2)
    """
    cities = []
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                try:
                    # 直接分割行，获取坐标
                    parts = line.split()
                    if len(parts) >= 2:
                        x, y = float(parts[0]), float(parts[1])
                        cities.append([x, y])
                except (ValueError, IndexError):
                    continue
    return np.array(cities)

def read_solution_file(file_path):
    """
    读取solution文件中的路径和成本
    
    :param file_path: solution文件路径
    :return: 列表，每个元素是一个元组(cost, path)
    """
    solutions = []
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line:
                parts = line.split()
                if len(parts) > 1:
                    try:
                        cost = int(parts[0])
                        path = [int(node) for node in parts[1:]]
                        solutions.append((cost, path))
                    except ValueError:
                        continue
    return solutions

def detect_dense_regions(cities, path, threshold_distance=3.5, min_region_size=3, max_regions=5):
    """
    使用K-means聚类算法直接将城市分为指定数量的区域
    
    :param cities: 城市坐标数组
    :param path: TSP路径
    :param threshold_distance: 不再使用，保留参数以兼容现有代码
    :param min_region_size: 不再使用，保留参数以兼容现有代码
    :param max_regions: 目标区域数量
    :return: 密集区域的边界列表 [(x_min, y_min, x_max, y_max, region_cities), ...]
    """
    from sklearn.cluster import KMeans
    
    n_cities = len(cities)
    print(f"使用K-means聚类算法将{n_cities}个城市直接分为{max_regions}个区域")
    
    # 确保聚类数量不超过城市数量
    n_clusters = min(max_regions, n_cities)
    if n_clusters < max_regions:
        print(f"警告: 聚类数量已从{max_regions}调整为{n_clusters}，因为城市数量为{n_cities}")
    
    # 使用K-means聚类将城市分为指定数量的区域
    kmeans = KMeans(n_clusters=n_clusters, init='k-means++', n_init=10, random_state=42).fit(cities)
    labels = kmeans.labels_
    
    # 根据聚类结果创建密集区域
    dense_regions = []
    for i in range(n_clusters):
        region_cities = [j for j in range(len(cities)) if labels[j] == i]
        if region_cities:
            region_points = cities[region_cities]
            x_min, y_min = np.min(region_points, axis=0) - 1.0  # 边界扩展1个单位
            x_max, y_max = np.max(region_points, axis=0) + 1.0
            dense_regions.append((x_min, y_min, x_max, y_max, region_cities))
    
    # 按区域大小排序，优先显示较大的区域
    dense_regions.sort(key=lambda x: len(x[4]), reverse=True)
    
    print(f"成功创建{len(dense_regions)}个区域")
    return dense_regions

def plot_enhanced_tsp_path(cities, path, instance_name, cost, solution_index, output_folder, dpi=300, split_layout=True):
    # 强制使用分区布局，确保能同时显示完整TSP路径和密集区域子图
    split_layout = True
    print(f"为{instance_name}启用分区布局模式，确保同时显示完整TSP路径和密集区域子图")
    """
    绘制增强版TSP路径，提高密集区域的可视化效果
    
    :param cities: 城市的坐标，形状为(n, 2)的数组
    :param path: TSP路径，列表中元素为城市的索引
    :param instance_name: 实例名称
    :param cost: 路径成本
    :param solution_index: 解决方案索引
    :param output_folder: 输出文件夹路径
    :param dpi: 图像分辨率
    :param split_layout: 是否使用分区布局
    """
    # 确保城市和路径的有效性
    if not isinstance(cities, np.ndarray) or cities.shape[1] != 2:
        raise ValueError("cities应该是一个形状为(n, 2)的numpy数组")
    if not isinstance(path, list) or not all(isinstance(i, int) for i in path):
        raise ValueError("path应该是一个包含城市索引的列表")
    
    # 创建画布，使用更大的尺寸以适应分区布局
    fig = plt.figure(figsize=(22, 20))  # 增加尺寸以更好地适应上下布局和子图
    
    # 使用GridSpec创建更灵活的布局
    if split_layout:
        # 创建GridSpec布局，上半部分为主图，下半部分为子图区域
        gs = plt.GridSpec(2, 1, height_ratios=[1.5, 1.0], hspace=0.3)  # 调整比例，增加主图比例，减小间距
        # 创建上半部分主图区域
        ax_main = fig.add_subplot(gs[0])
        print("使用优化的分区布局模式：上部为主图，下部为密集区域子图")
    else:
        # 传统布局：全图占据整个画布
        ax_main = fig.add_subplot(111)
    
    # 设置更好的颜色方案
    city_color = '#D81B60'  # 鲜艳的红色
    path_color = '#1E88E5'  # 鲜艳的蓝色
    background_color = '#F5F5F5'  # 浅灰色背景
    grid_color = '#E0E0E0'  # 网格颜色
    
    # 设置背景色
    ax_main.set_facecolor(background_color)
    
    # 通过路径连接城市
    path_cities = np.array([cities[i] for i in path if i < len(cities)])
    
    if len(path_cities) > 0:
        # 绘制路径线条，使用渐变色和路径效果增强可视性
        for i in range(len(path_cities)-1):
            # 添加路径效果，使线条更清晰
            line = ax_main.plot([path_cities[i][0], path_cities[i+1][0]], 
                         [path_cities[i][1], path_cities[i+1][1]], 
                         color=path_color, linewidth=1.5, zorder=1)
            # 添加路径效果，使线条更清晰
            line[0].set_path_effects([PathEffects.withStroke(linewidth=2.5, foreground='white')])
        
        # 闭合路径 - 无论起点和终点是否相同，都确保路径闭合
        if len(path_cities) > 1:
            line = ax_main.plot([path_cities[-1][0], path_cities[0][0]], 
                         [path_cities[-1][1], path_cities[0][1]], 
                         color=path_color, linewidth=1.5, zorder=1)
            line[0].set_path_effects([PathEffects.withStroke(linewidth=2.5, foreground='white')])
    
    # 绘制城市点，使用更大的点和边缘效果
    scatter = ax_main.scatter(cities[:, 0], cities[:, 1], 
                      c=city_color, s=50, edgecolors='white', linewidth=0.8, 
                      zorder=2, alpha=0.9)
    
    # 检测密集区域
    # 根据实例特点调整参数，确保能识别出指定数量的密集区域
    
    # 定义不同实例的目标区域数量
    instance_region_map = {
        'composite1_28': 3,
        'composite2_34': 4,
        'composite3_22': 4,
        'composite4_33': 4,
        'composite5_35': 4,
        'composite6_39': 7,
        'composite7_42': 5,
        'composite8_45': 4,
        'composite9_48': 4,
        'composite10_55': 5,
        'composite11_59': 5,
        'composite12_60': 5,
        'composite13_66': 5
    }
    
    # 确定当前实例的目标区域数量
    target_regions = 5  # 默认值
    for instance_name_key, region_count in instance_region_map.items():
        if instance_name_key in instance_name:
            target_regions = region_count
            print(f"为{instance_name}设置目标区域数量: {target_regions}")
            break
    
    # 直接使用目标区域数量进行聚类，不再使用动态阈值
    dense_regions = detect_dense_regions(cities, path, max_regions=target_regions)
    print(f"为{instance_name}使用K-means聚类创建了{len(dense_regions)}个区域，目标区域数={target_regions}")

    
    # 智能标注城市，避免标签重叠
    texts = []
    for i, city in enumerate(cities):
        # 为每个城市创建文本标签
        txt = ax_main.text(city[0], city[1], str(i), fontsize=9, ha='center', va='center',
                   bbox=dict(boxstyle="round,pad=0.3", fc='white', ec="gray", alpha=0.7),
                   zorder=3)
        texts.append(txt)
    
    # 使用adjust_text库避免文本重叠
    try:
        adjust_text(texts, arrowprops=dict(arrowstyle='->', color='gray'))
    except ImportError:
        print("警告: adjust_text库未安装，文本可能会重叠")
        print("可以通过运行 'pip install adjustText' 安装该库")
    
    # 设置图表样式
    ax_main.set_title(f"{instance_name} - Solution {solution_index+1} - Cost: {cost}", fontsize=16,fontweight='bold')
    ax_main.set_xlabel("X", fontsize=12)
    ax_main.set_ylabel("Y", fontsize=12)
    ax_main.grid(True, linestyle='--', alpha=0.7, color=grid_color)
    
    # 如果有密集区域，添加子图放大显示
    if dense_regions:
        # 确定子图布局
        n_regions = len(dense_regions)  # 显示所有检测到的区域
        
        # 确保使用分区布局
        if not split_layout:
            print("警告: 强制启用分区布局以确保子图正确显示")
            split_layout = True
            # 重新创建GridSpec布局
            gs = plt.GridSpec(2, 1, height_ratios=[1.2, 1.0], hspace=0.4)
            # 将当前主图内容转移到新的GridSpec布局中
            ax_main_new = fig.add_subplot(gs[0])
            ax_main_new.set_facecolor(background_color)
            # 复制主图内容
            for item in ax_main.get_children():
                if isinstance(item, plt.Line2D):
                    ax_main_new.plot(item.get_xdata(), item.get_ydata(), color=item.get_color(), 
                                    linewidth=item.get_linewidth(), zorder=item.get_zorder())
                elif isinstance(item, plt.collections.PathCollection):  # 处理散点图
                    ax_main_new.scatter(item.get_offsets()[:, 0], item.get_offsets()[:, 1],
                                       c=item.get_facecolor(), s=item.get_sizes(), 
                                       edgecolors=item.get_edgecolor(), linewidth=item.get_linewidth(),
                                       zorder=item.get_zorder(), alpha=item.get_alpha())
            # 更新主图引用
            ax_main = ax_main_new
        
        # 在主图中标记所有密集区域
        region_colors = plt.cm.tab10(np.linspace(0, 1, n_regions))  # 使用不同颜色区分区域
        
        for i, (x_min, y_min, x_max, y_max, region_cities) in enumerate(dense_regions[:n_regions]):
            # 在主图中标记密集区域，使用不同颜色的边框
            # 创建带透明度的填充颜色
            face_color = list(region_colors[i])
            face_color[3] = 0.2  # 设置透明度为0.2
            rect = Rectangle((x_min, y_min), x_max-x_min, y_max-y_min, 
                             linewidth=2.0, edgecolor=region_colors[i], facecolor=face_color, 
                             linestyle='--', zorder=4)
            ax_main.add_patch(rect)
            ax_main.text(x_min, y_max, f"Region {i+1}", color=region_colors[i], fontweight='bold', fontsize=11,
                       bbox=dict(facecolor='white', ec=region_colors[i], alpha=0.9, boxstyle="round,pad=0.3"))
        
        # 根据布局方式创建子图
        if split_layout:
            # 创建下半部分子图区域
            # 根据区域数量确定网格布局
            # 动态调整行列数以适应不同数量的区域
            if n_regions <= 6:
                rows, cols = 2, 3
            elif n_regions <= 9:
                rows, cols = 3, 3
            elif n_regions <= 12:
                rows, cols = 3, 4
            else:
                rows, cols = 4, 4
            print(f"创建{rows}行{cols}列的子图网格布局，用于显示{min(n_regions, rows*cols)}个密集区域")
                
            # 在下半部分创建子网格，嵌套在主GridSpec的第二行
            # 调整间距参数使布局更加紧凑和美观
            grid_spec = gs[1].subgridspec(rows, cols, wspace=0.2, hspace=0.3)  # 减小子图间距，使布局更紧凑
        
        # 绘制每个密集区域的子图
        for i, (x_min, y_min, x_max, y_max, region_cities) in enumerate(dense_regions[:n_regions]):
            if i >= n_regions:
                break
                
            # 根据布局方式确定子图位置
            if split_layout:
                # 使用网格布局，在下半部分创建子图
                row_idx = i // cols
                col_idx = i % cols
                ax_sub = fig.add_subplot(grid_spec[row_idx, col_idx])
            else:
                # 使用传统布局（垂直排列在右侧）
                if n_regions == 1:
                    ax_sub = fig.add_axes([0.72, 0.15, 0.25, 0.25])
                else:
                    # 计算子图位置，确保不重叠
                    y_pos = 0.95 - (i * 0.9 / n_regions) - 0.05
                    ax_sub = fig.add_axes([0.72, y_pos, 0.25, 0.25])
                
            # 设置子图标题和样式
            ax_sub.set_title(f"Dense Region {i+1}", fontsize=12, fontweight='bold', color=region_colors[i])
            ax_sub.set_facecolor('#F8F9FA')  # 设置子图背景色，增加对比度
            
            # 添加区域信息标签，显示包含的城市数量
            city_count = len(region_cities)
            ax_sub.text(0.02, 0.02, f"Contains {city_count} cities", transform=ax_sub.transAxes, 
                      fontsize=9, color='#444444', bbox=dict(facecolor='white', alpha=0.7, pad=2))
            
            # 添加区域边界标记，使子图更加清晰
            rect = Rectangle((x_min, y_min), x_max-x_min, y_max-y_min, 
                             linewidth=1.5, edgecolor=region_colors[i], facecolor='none', 
                             linestyle='--', zorder=1)
            ax_sub.add_patch(rect)
            
            # 添加边框，使用与区域相同的颜色
            for spine in ax_sub.spines.values():
                spine.set_edgecolor(region_colors[i])
                spine.set_linewidth(2.0)
            
            # 绘制子图中的城市点，使用更大的点
            ax_sub.scatter(cities[region_cities, 0], cities[region_cities, 1], 
                          c=city_color, s=100, edgecolors='white', linewidth=1.5, zorder=2)
            
            # 绘制子图中的路径，使用更粗的线条和路径效果
            path_set = set(path)
            for j in range(len(path)-1):
                if path[j] in region_cities and path[j+1] in region_cities:
                    line = ax_sub.plot([cities[path[j]][0], cities[path[j+1]][0]], 
                                      [cities[path[j]][1], cities[path[j+1]][1]], 
                                      color=path_color, linewidth=3.0, zorder=1)
                    # 添加路径效果，使线条更清晰
                    line[0].set_path_effects([PathEffects.withStroke(linewidth=4.5, foreground='white')])
            
            # 闭合子图中的路径 - 确保起点和终点相连
            if path[0] in region_cities and path[-1] in region_cities and len(path) > 1:
                line = ax_sub.plot([cities[path[-1]][0], cities[path[0]][0]], 
                                  [cities[path[-1]][1], cities[path[0]][1]], 
                                  color=path_color, linewidth=3.0, zorder=1)
                line[0].set_path_effects([PathEffects.withStroke(linewidth=4.5, foreground='white')])
            
            # 为子图添加连接线，显示路径方向
            for j in range(len(path)-1):
                if path[j] in region_cities and path[j+1] in region_cities:
                    # 添加箭头指示方向
                    mid_x = (cities[path[j]][0] + cities[path[j+1]][0]) / 2
                    mid_y = (cities[path[j]][1] + cities[path[j+1]][1]) / 2
                    dx = cities[path[j+1]][0] - cities[path[j]][0]
                    dy = cities[path[j+1]][1] - cities[path[j]][1]
                    # 计算箭头方向
                    arrow_len = np.sqrt(dx**2 + dy**2)
                    if arrow_len > 0:
                        dx, dy = dx/arrow_len, dy/arrow_len
                        ax_sub.arrow(mid_x-dx*2, mid_y-dy*2, dx*4, dy*4, 
                                    head_width=2.0, head_length=2.0, fc=path_color, ec=path_color, zorder=3)
            
            # 智能标注子图中的城市，避免标签重叠
            texts = []
            for city_idx in region_cities:
                txt = ax_sub.text(cities[city_idx][0], cities[city_idx][1], str(city_idx), 
                              fontsize=11, ha='center', va='center', fontweight='bold',
                              bbox=dict(boxstyle="round,pad=0.3", fc='white', ec="#444444", alpha=0.95),
                              zorder=4)
                texts.append(txt)
            
            # 尝试使用adjust_text避免标签重叠
            try:
                adjust_text(texts, arrowprops=dict(arrowstyle='->', color='#444444', lw=1.0))
            except Exception:
                # 如果adjust_text失败，使用简单的偏移策略
                for t in texts:
                    t.set_position((t.get_position()[0] + np.random.uniform(-1, 1),
                                    t.get_position()[1] + np.random.uniform(-1, 1)))
            
            # 设置子图范围，使用更大的边距以便更好地显示
            margin = (x_max - x_min) * 0.15
            ax_sub.set_xlim(x_min - margin, x_max + margin)
            ax_sub.set_ylim(y_min - margin, y_max + margin)
            ax_sub.grid(True, linestyle='--', alpha=0.7, color='#CCCCCC')
            
            # 添加边框，使子图更加突出
            for spine in ax_sub.spines.values():
                spine.set_edgecolor('#888888')
                spine.set_linewidth(1.5)
    
    # 调整布局，确保图形元素不重叠
    if not split_layout:
        plt.tight_layout()
    else:
        # 对于分区布局，使用fig.subplots_adjust代替tight_layout
        # 调整边距参数，确保有足够的空间显示所有元素
        fig.subplots_adjust(left=0.04, right=0.96, top=0.96, bottom=0.04, hspace=0.25, wspace=0.2)
        print("应用优化的分区布局调整，减小边距和间距以最大化显示区域")
    
    # # 添加整体标题
    # if split_layout and dense_regions:
    #     fig.suptitle(f"{instance_name} - TSP Path with {len(dense_regions)} Dense Regions", fontsize=18, fontweight='bold', y=0.98)
    #     print(f"添加整体标题: {instance_name} - TSP Path with {len(dense_regions)} Dense Regions")
    
    # 创建实例专用文件夹
    instance_folder = os.path.join(output_folder, instance_name)
    if not os.path.exists(instance_folder):
        os.makedirs(instance_folder)
    
    # 获取当前时间作为时间戳
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存高分辨率图像，文件名中包含时间戳
    output_filename = f"{instance_name}_{solution_index+1}_{timestamp}_enhanced.png"
    output_path = os.path.join(instance_folder, output_filename)
    plt.savefig(output_path, dpi=dpi, bbox_inches='tight')
    print(f"图像已保存至: {output_path}")
    plt.close()
    
    return output_path

if __name__ == "__main__":
    # 设置输入和输出文件夹
    solution_file = "c:\\Users\\<USER>\\Desktop\\EoH-main - idea - 0312-feedback\\EoH-main\\results\\solutions\\composite7_42_20250406_202632.solution"
    tsp_file = "c:\\Users\\<USER>\\Desktop\\EoH-main - idea - 0312-feedback\\benchmark_MMTSP\\composite7_42.tsp"
    output_folder = "c:\\Users\\<USER>\\Desktop\\EoH-main - idea - 0312-feedback\\results\\enhanced_solution_plots_perturbed"
    
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    # 提取实例名称
    # 从tsp_file路径中提取实例名称
    instance_name = os.path.basename(tsp_file).split('.')[0]
    print(f"处理实例: {instance_name}")
    
    if os.path.exists(tsp_file):
        # 读取城市坐标
        cities = read_tsp_file(tsp_file)
        
        # 读取解决方案
        solutions = read_solution_file(solution_file)
        
        # 为每个解决方案绘制增强路径
        for i, (cost, path) in enumerate(solutions):
            # 使用分区布局绘制
            output_file = plot_enhanced_tsp_path(cities, path, instance_name, cost, i, output_folder, split_layout=True)
            print(f"  - 已绘制增强解决方案 {i+1}/{len(solutions)}")
            print(f"  - 保存至: {output_file}")