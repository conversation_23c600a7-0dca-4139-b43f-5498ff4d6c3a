#!/usr/bin/env python3
"""
创建项目重组所需的目录结构
"""

import os

def create_directory_structure():
    """创建新的项目目录结构"""
    
    # 定义目录结构
    directories = [
        # 源代码目录
        "src/core/algorithms",
        "src/core/optimization", 
        "src/core/data",
        "src/experts/base",
        "src/experts/analysis",
        "src/experts/evolution",
        "src/experts/strategy",
        "src/experts/management",
        "src/experts/prompts",
        "src/api",
        "src/knowledge",
        "src/utils/analyzers",
        "src/utils/trackers",
        "src/utils/extractors",
        "src/config",
        
        # 数据目录
        "data/benchmark/MMTSP",
        "data/knowledge_base",
        
        # 工具目录
        "tools/analysis",
        "tools/visualization", 
        "tools/testing",
        "tools/scheduling",
        
        # 文档和测试目录
        "docs",
        "tests"
    ]
    
    # 创建目录
    for directory in directories:
        try:
            os.makedirs(directory, exist_ok=True)
            print(f"创建目录: {directory}")
        except Exception as e:
            print(f"创建目录 {directory} 失败: {e}")
    
    # 创建__init__.py文件
    init_files = [
        "src/__init__.py",
        "src/core/__init__.py",
        "src/core/algorithms/__init__.py",
        "src/core/optimization/__init__.py",
        "src/core/data/__init__.py",
        "src/experts/__init__.py",
        "src/experts/base/__init__.py",
        "src/experts/analysis/__init__.py",
        "src/experts/evolution/__init__.py",
        "src/experts/strategy/__init__.py",
        "src/experts/management/__init__.py",
        "src/experts/prompts/__init__.py",
        "src/api/__init__.py",
        "src/knowledge/__init__.py",
        "src/utils/__init__.py",
        "src/utils/analyzers/__init__.py",
        "src/utils/trackers/__init__.py",
        "src/utils/extractors/__init__.py",
        "src/config/__init__.py"
    ]
    
    for init_file in init_files:
        try:
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write('"""Package initialization file"""\n')
            print(f"创建文件: {init_file}")
        except Exception as e:
            print(f"创建文件 {init_file} 失败: {e}")

if __name__ == "__main__":
    create_directory_structure()
    print("目录结构创建完成!")
