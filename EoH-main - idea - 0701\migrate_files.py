#!/usr/bin/env python3
"""
文件迁移脚本 - 将文件按功能分类迁移到新的目录结构中
"""

import os
import shutil

def migrate_files():
    """执行文件迁移"""
    
    # 定义文件迁移映射
    file_migrations = {
        # 核心算法模块
        "MoE-main/idea/gls_evol_enhanced.py": "src/core/algorithms/gls_evol_enhanced.py",
        "MoE-main/idea/gls_operators.py": "src/core/algorithms/gls_operators.py", 
        "MoE-main/idea/gls_run.py": "src/core/algorithms/gls_run.py",
        "MoE-main/idea/guided_local_search_with_similarity.py": "src/core/algorithms/guided_local_search_with_similarity.py",
        "MoE-main/idea/optimized_topology_aware_perturbation.py": "src/core/algorithms/optimized_topology_aware_perturbation.py",
        
        # 优化模块
        "MoE-main/idea/path_similarity_optimizer.py": "src/core/optimization/path_similarity_optimizer.py",
        "MoE-main/idea/greedy_path_generator.py": "src/core/optimization/greedy_path_generator.py",
        "MoE-main/idea/progress_calculator.py": "src/core/optimization/progress_calculator.py",
        
        # 数据处理模块
        "MoE-main/idea/loadinstance.py": "src/core/data/loadinstance.py",
        "MoE-main/idea/initpop.py": "src/core/data/initpop.py",
        
        # 专家基础模块
        "MoE-main/idea/expert_base.py": "src/experts/base/expert_base.py",
        
        # 分析专家
        "MoE-main/idea/landscape_expert.py": "src/experts/analysis/landscape_expert.py",
        "MoE-main/idea/stats_expert.py": "src/experts/analysis/stats_expert.py",
        "MoE-main/idea/path_expert.py": "src/experts/analysis/path_expert.py",
        "MoE-main/idea/elite_expert.py": "src/experts/analysis/elite_expert.py",
        
        # 进化专家
        "MoE-main/idea/exploration_expert.py": "src/experts/evolution/exploration_expert.py",
        "MoE-main/idea/exploitation_expert.py": "src/experts/evolution/exploitation_expert.py",
        "MoE-main/idea/assessment_expert.py": "src/experts/evolution/assessment_expert.py",
        
        # 策略专家
        "MoE-main/idea/strategy_expert.py": "src/experts/strategy/strategy_expert.py",
        
        # 管理模块
        "MoE-main/idea/collaboration_manager.py": "src/experts/management/collaboration_manager.py",
        
        # 提示词模块
        "MoE-main/idea/experts_prompt.py": "src/experts/prompts/experts_prompt.py",
        
        # API模块
        "MoE-main/idea/api_general.py": "src/api/api_general.py",
        
        # 知识库模块
        "MoE-main/idea/knowledge_base.py": "src/knowledge/knowledge_base.py",
        "MoE-main/idea/exploration_knowledge_base.py": "src/knowledge/exploration_knowledge_base.py",
        
        # 工具模块 - 分析器
        "MoE-main/idea/stats_analyzer.py": "src/utils/analyzers/stats_analyzer.py",
        "MoE-main/idea/path_structure_analyzer.py": "src/utils/analyzers/path_structure_analyzer.py",
        
        # 工具模块 - 跟踪器
        "MoE-main/idea/time_tracker.py": "src/utils/trackers/time_tracker.py",
        "MoE-main/idea/strategy_tracker.py": "src/utils/trackers/strategy_tracker.py",
        
        # 工具模块 - 提取器
        "MoE-main/idea/idea_extractor.py": "src/utils/extractors/idea_extractor.py",
        
        # 通用工具
        "MoE-main/idea/utils.py": "src/utils/utils.py",
        "MoE-main/idea/jit_warmup.py": "src/utils/jit_warmup.py",
        
        # 配置模块
        "MoE-main/idea/config.py": "src/config/config.py",
        "MoE-main/idea/log_config.py": "src/config/log_config.py",
        
        # 主程序
        "MoE-main/idea/moe_main.py": "src/main.py",
    }
    
    # 执行文件迁移
    for source, destination in file_migrations.items():
        try:
            if os.path.exists(source):
                # 确保目标目录存在
                os.makedirs(os.path.dirname(destination), exist_ok=True)
                # 复制文件
                shutil.copy2(source, destination)
                print(f"迁移: {source} -> {destination}")
            else:
                print(f"源文件不存在: {source}")
        except Exception as e:
            print(f"迁移失败 {source} -> {destination}: {e}")

if __name__ == "__main__":
    migrate_files()
    print("文件迁移完成!")
