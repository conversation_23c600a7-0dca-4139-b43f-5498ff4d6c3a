import numpy as np
from collections import defaultdict, Counter
import logging

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

class PathStructureAnalyzer:
    """TSP路径结构分析器，用于识别和评估高质量的序列结构"""
    
    @staticmethod
    def identify_high_quality_edges(populations, distance_matrix):
        """识别高质量边
        
        Args:
            populations: 种群列表
            distance_matrix: 距离矩阵
            
        Returns:
            list: 高质量边列表
        """
        if not populations or not distance_matrix.any():
            return []
            
        # 计算每条边的出现频率和平均成本
        edge_frequency = defaultdict(int)
        edge_costs = defaultdict(list)
        
        for individual in populations:
            tour = individual['tour']
            cost = individual['cur_cost']
            n = len(tour)
            
            # 更新边频率和成本
            for i in range(n):
                city1 = tour[i]
                city2 = tour[(i + 1) % n]
                edge = tuple(sorted([city1, city2]))  # 无向边
                
                edge_frequency[edge] += 1
                
                # 估算边的成本贡献
                # 添加索引检查，防止索引越界
                if 0 <= city1 < distance_matrix.shape[0] and 0 <= city2 < distance_matrix.shape[0]:
                    edge_cost = distance_matrix[city1][city2]
                    edge_costs[edge].append(edge_cost)
                else:
                    # 记录错误并跳过此边
                    logger.error(f"索引越界: city1={city1}, city2={city2}, distance_matrix.shape={distance_matrix.shape}")
                    continue
        
        # 计算每条边的平均成本
        edge_avg_costs = {}
        for edge, costs in edge_costs.items():
            if costs:  # 确保costs列表不为空
                edge_avg_costs[edge] = sum(costs) / len(costs)
        
        # 找出高频且成本较低的边
        high_quality_edges = []
        
        if edge_avg_costs:
            # 计算成本阈值（低于平均成本的边）
            avg_cost = sum(edge_avg_costs.values()) / len(edge_avg_costs)
            cost_threshold = avg_cost * 0.8  # 低于平均成本20%
            
            # 计算频率阈值（出现在超过50%的路径中）
            freq_threshold = len(populations) * 0.5
            
            for edge, freq in edge_frequency.items():
                if freq >= freq_threshold and edge_avg_costs[edge] <= cost_threshold:
                    high_quality_edges.append({
                        'edge': edge,
                        'frequency': freq / len(populations),
                        'avg_cost': edge_avg_costs[edge]
                    })
        
        return high_quality_edges
    
    @staticmethod
    def identify_common_subpaths(populations):
        """识别公共子路径
        
        Args:
            populations: 种群列表
            
        Returns:
            list: 公共子路径列表
        """
        if not populations:
            return []
            
        # 提取所有子路径（长度为3）
        subpath_counter = Counter()
        
        for individual in populations:
            tour = individual['tour']
            n = len(tour)
            
            # 提取所有长度为3的子路径
            for i in range(n):
                # 处理常规子路径
                if i + 2 < n:
                    subpath = tuple(tour[i:i+3])
                    subpath_counter[subpath] += 1
                # 处理跨越路径起点的子路径
                else:
                    remaining = 3 - (n - i)
                    subpath = tuple(tour[i:] + tour[:remaining])
                    subpath_counter[subpath] += 1
        
        # 找出高频子路径（出现在超过30%的路径中）
        threshold = len(populations) * 0.3
        common_subpaths = []
        
        for subpath, count in subpath_counter.items():
            if count >= threshold:
                common_subpaths.append({
                    'subpath': subpath,
                    'frequency': count / len(populations)
                })
        
        # 按频率排序
        common_subpaths.sort(key=lambda x: x['frequency'], reverse=True)
        
        return common_subpaths[:10]  # 返回前10个最常见的子路径
    
    @staticmethod
    def analyze_edge_frequency(populations):
        """分析边的出现频率
        
        Args:
            populations: 种群列表
            
        Returns:
            dict: 边频率分析结果
        """
        if not populations:
            return {}
            
        # 计算每条边的出现频率
        edge_counter = Counter()
        
        for individual in populations:
            tour = individual['tour']
            n = len(tour)
            
            for i in range(n):
                city1 = tour[i]
                city2 = tour[(i + 1) % n]
                
                # 确保城市索引有效（不超过最大城市数）
                max_city_index = max(max(tour) if tour else 0, city1, city2)
                if max_city_index >= n:
                    logger.warning(f"城市索引超出范围: city1={city1}, city2={city2}, tour_length={n}")
                    continue
                
                # 使用排序后的元组表示无向边，与identify_high_quality_edges方法保持一致
                edge = tuple(sorted([city1, city2]))
                edge_counter[edge] += 1
        
        # 转换为频率
        total_paths = len(populations)
        edge_frequency = {str(edge): count/total_paths for edge, count in edge_counter.items()}
        
        # 分类边的频率
        high_freq_edges = []
        medium_freq_edges = []
        low_freq_edges = []
        
        for edge, freq in edge_frequency.items():
            if freq >= 0.7:  # 高频边（出现在70%以上的路径中）
                high_freq_edges.append({'edge': edge, 'frequency': freq})
            elif freq >= 0.4:  # 中频边（出现在40%-70%的路径中）
                medium_freq_edges.append({'edge': edge, 'frequency': freq})
            elif freq >= 0.2:  # 低频边（出现在20%-40%的路径中）
                low_freq_edges.append({'edge': edge, 'frequency': freq})
        
        return {
            'high_frequency_edges': high_freq_edges,
            'medium_frequency_edges': medium_freq_edges,
            'low_frequency_edges': low_freq_edges
        }
    
    @staticmethod
    def identify_low_quality_regions(populations, distance_matrix):
        """识别低质量区域
        
        Args:
            populations: 种群列表
            distance_matrix: 距离矩阵
            
        Returns:
            list: 低质量区域列表
        """
        if not populations or not distance_matrix.any():
            return []
            
        # 计算每条边的成本
        edge_costs = {}
        
        for individual in populations:
            tour = individual['tour']
            n = len(tour)
            
            for i in range(n):
                city1 = tour[i]
                city2 = tour[(i + 1) % n]
                
                # 使用排序后的元组表示无向边，与其他方法保持一致
                edge = tuple(sorted([city1, city2]))
                
                if edge not in edge_costs:
                    # 添加索引检查，防止索引越界
                    if 0 <= city1 < distance_matrix.shape[0] and 0 <= city2 < distance_matrix.shape[0]:
                        edge_costs[edge] = distance_matrix[city1][city2]
                    else:
                        # 记录错误并跳过此边
                        logger.error(f"索引越界: city1={city1}, city2={city2}, distance_matrix.shape={distance_matrix.shape}")
                        continue
        
        # 找出高成本边
        if not edge_costs:
            return []
            
        avg_cost = sum(edge_costs.values()) / len(edge_costs)
        high_cost_threshold = avg_cost * 1.5  # 高于平均成本50%
        
        high_cost_edges = [edge for edge, cost in edge_costs.items() 
                          if cost >= high_cost_threshold]
        
        # 识别连续的高成本边形成的区域
        low_quality_regions = []
        
        for individual in populations:
            tour = individual['tour']
            n = len(tour)
            
            # 寻找连续的高成本边
            regions = []
            current_region = []
            
            for i in range(n):
                city1 = tour[i]
                city2 = tour[(i + 1) % n]
                
                # 确保城市索引有效
                if city1 >= distance_matrix.shape[0] or city2 >= distance_matrix.shape[0]:
                    logger.warning(f"城市索引超出范围: city1={city1}, city2={city2}, distance_matrix.shape={distance_matrix.shape}")
                    continue
                
                # 使用排序后的元组表示无向边
                edge = tuple(sorted([city1, city2]))
                
                if edge in high_cost_edges:
                    if not current_region:
                        current_region = [city1]
                    current_region.append(city2)
                else:
                    if len(current_region) >= 3:  # 至少包含3个城市的区域
                        regions.append(current_region)
                    current_region = []
            
            # 检查最后一个区域
            if len(current_region) >= 3:
                regions.append(current_region)
            
            # 添加到结果中
            for region in regions:
                region_cost = sum(distance_matrix[region[i]][region[i+1]] 
                                for i in range(len(region)-1))
                
                low_quality_regions.append({
                    'region': region,
                    'cost': region_cost,
                    'size': len(region)
                })
        
        # 去重并按成本排序
        unique_regions = []
        region_strs = set()
        
        for region in low_quality_regions:
            region_str = str(region['region'])
            if region_str not in region_strs:
                region_strs.add(region_str)
                unique_regions.append(region)
        
        unique_regions.sort(key=lambda x: x['cost'], reverse=True)
        
        return unique_regions[:5]  # 返回前5个最高成本的区域