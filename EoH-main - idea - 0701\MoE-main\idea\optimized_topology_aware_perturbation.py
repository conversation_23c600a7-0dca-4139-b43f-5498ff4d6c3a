import time
import random
import numpy as np
from time_tracker import time_tracker
import logging

# 全局缓存字典，用于存储已计算过的解对之间的共同段和差异段
segment_cache = {}


def segments_overlap(start1, end1, start2, end2, n, end_time=None):
    """
    检查两个段是否重叠，考虑环形路径
    
    参数:
        start1, end1: 第一个段的起点和终点
        start2, end2: 第二个段的起点和终点
        n: 路径长度
        end_time: 结束时间，用于检查是否超时
    
    返回:
        bool: 如果两个段重叠则返回True，否则返回False
    """
    # 检查是否超时
    if end_time is not None and time.time() >= end_time:
        return True  # 如果超时，假设重叠以避免继续处理
    
    # 处理环绕情况
    # 将段表示为区间集合
    intervals1 = []
    if start1 <= end1:
        intervals1.append((start1, end1))
    else:  # 环绕情况
        intervals1.append((start1, n))
        intervals1.append((0, end1))
    
    intervals2 = []
    if start2 <= end2:
        intervals2.append((start2, end2))
    else:  # 环绕情况
        intervals2.append((start2, n))
        intervals2.append((0, end2))
    
    # 检查任意两个区间是否重叠
    for s1, e1 in intervals1:
        for s2, e2 in intervals2:
            # 两个区间重叠的条件：一个区间的起点在另一个区间内，或者终点在另一个区间内
            if (s1 <= s2 < e1) or (s1 < e2 <= e1) or (s2 <= s1 < e2) or (s2 < e1 <= e2):
                return True
    
    return False

def get_cache_key(sol1, sol2):
    """
    生成缓存键，确保相同的两个解无论顺序如何都能得到相同的键
    """
    # 将解转换为元组以便哈希
    sol1_tuple = tuple(sol1)
    sol2_tuple = tuple(sol2)
    # 确保键的顺序一致性
    if hash(sol1_tuple) < hash(sol2_tuple):
        return (sol1_tuple, sol2_tuple)
    else:
        return (sol2_tuple, sol1_tuple)

def find_common_segments(sol1, sol2, min_length=3, end_time=None):
    """
    优化版找出两个解中的共同段（连续的城市序列）
    使用字典缓存和更高效的匹配算法
    
    参数:
        sol1, sol2: 两个解
        min_length: 最小段长度
        end_time: 结束时间，用于检查是否超时
    """
    # 检查是否超时
    if end_time is not None and time.time() >= end_time:
        return []
        
    # 检查缓存中是否已有结果
    cache_key = get_cache_key(sol1, sol2)
    if cache_key in segment_cache and 'common' in segment_cache[cache_key]:
        # 更新缓存使用时间
        cache_usage_time[cache_key] = time.time()
        
        # 更新缓存统计
        if 'total_calls' in cache_stats:
            cache_stats['total_calls'] += 1
            cache_stats['hits'] += 1
        
        return segment_cache[cache_key]['common']
    
    # 检查缓存大小并在必要时清理
    # 每处理50个请求检查一次，或当缓存大小超过阈值时
    if 'total_calls' in cache_stats and (cache_stats['total_calls'] % 50 == 0 or len(segment_cache) > 900):
        dynamic_cache_size = 1000
        if len(sol1) > 100:  # 大规模问题
            dynamic_cache_size = 800
        elif len(sol1) < 50:  # 小规模问题
            dynamic_cache_size = 1200
            
        force_cleanup = len(segment_cache) > dynamic_cache_size * 0.9
        limit_cache_size(max_size=dynamic_cache_size, force_cleanup=force_cleanup)
    
    n = len(sol1)
    common_segments = []
    
    # 构建sol2的位置映射
    pos_map = {city: idx for idx, city in enumerate(sol2)}
    
    # 遍历sol1寻找共同段
    i = 0
    while i < n:
        # 每处理一个城市就检查一次是否超时，最大程度提高超时检测粒度
        if end_time is not None and time.time() >= end_time:
            # 如果超时，返回已找到的段
            return common_segments
            
        # 检查当前位置是否在sol2中存在
        if sol1[i] in pos_map:
            j = pos_map[sol1[i]]
            length = 1
            
            # 尝试扩展共同段
            while length < n and sol1[(i + length) % n] in pos_map and \
                  sol1[(i + length) % n] == sol2[(j + length) % n]:
                length += 1
                # 每扩展一个长度就检查一次是否超时，最大程度提高超时检测粒度
                if end_time is not None and time.time() >= end_time:
                    # 如果超时但已找到有效段，添加到结果中
                    if length >= min_length:
                        common_segments.append((i, (i + length) % n))
                    return common_segments
            
            # 检查是否达到最小长度要求
            if length >= min_length:
                common_segments.append((i, (i + length) % n))
                i += length  # 跳过已匹配部分
            else:
                i += 1
        else:
            i += 1
    
    # 存入缓存
    if cache_key not in segment_cache:
        segment_cache[cache_key] = {}
    segment_cache[cache_key]['common'] = common_segments
    # 更新缓存使用时间
    cache_usage_time[cache_key] = time.time()
    
    return common_segments

def find_different_segments(sol1, sol2, min_length=2, end_time=None):
    """
    找出两个解中的差异段
    使用缓存减少重复计算
    
    参数:
        sol1, sol2: 两个解
        min_length: 最小段长度
        end_time: 结束时间，用于检查是否超时
    """
    # 检查是否超时
    if end_time is not None and time.time() >= end_time:
        return []
        
    # 检查缓存中是否已有结果
    cache_key = get_cache_key(sol1, sol2)
    if cache_key in segment_cache and 'different' in segment_cache[cache_key]:
        # 更新缓存使用时间
        cache_usage_time[cache_key] = time.time()
        
        # 更新缓存统计
        if 'total_calls' in cache_stats:
            cache_stats['total_calls'] += 1
            cache_stats['hits'] += 1
            
        # 检查是否需要清理缓存 - 每50次调用或缓存大小超过阈值时
        if cache_stats['total_calls'] % 50 == 0 or len(segment_cache) > 900:
            dynamic_cache_size = 1000
            if len(sol1) > 100:  # 大规模问题
                dynamic_cache_size = 800
            elif len(sol1) < 50:  # 小规模问题
                dynamic_cache_size = 1200
                
            force_cleanup = len(segment_cache) > dynamic_cache_size * 0.85
            limit_cache_size(max_size=dynamic_cache_size, force_cleanup=force_cleanup)
            
        return segment_cache[cache_key]['different']
    
    n = len(sol1)
    different_segments = []
    
    # 先找出共同段，优先使用缓存
    if cache_key in segment_cache and 'common' in segment_cache[cache_key]:
        common_segments = segment_cache[cache_key]['common']
        # 更新缓存使用时间
        cache_usage_time[cache_key] = time.time()
    else:
        # 传递end_time参数给find_common_segments
        common_segments = find_common_segments(sol1, sol2, min_length=3, end_time=end_time)
        # 检查是否在find_common_segments中超时
        if end_time is not None and time.time() >= end_time:
            return []
    
    # 如果没有共同段，整个路径都是差异段
    if not common_segments:
        different_segments = [(0, n)]
    else:
        # 排序共同段
        common_segments.sort(key=lambda x: x[0])
        
        # 找出共同段之间的差异段
        prev_end = common_segments[-1][1]
        for start_idx, (start, end) in enumerate(common_segments):
            # 每处理一个段就检查一次是否超时，最大程度提高超时检测粒度
            if end_time is not None and time.time() >= end_time:
                break
                
            if (start - prev_end) % n >= min_length:
                different_segments.append((prev_end % n, start))
            prev_end = end
    
    # 存入缓存
    if cache_key not in segment_cache:
        segment_cache[cache_key] = {}
    segment_cache[cache_key]['different'] = different_segments
    # 更新缓存使用时间
    cache_usage_time[cache_key] = time.time()
    
    return different_segments

def calculate_similarity(path, sol, similarity_cache=None, end_time=None, early_stop_threshold=0.85):
    """
    计算两个解之间的相似度，使用缓存减少计算
    增强版：添加早期终止条件和更频繁的超时检查
    
    参数:
        path, sol: 两个解
        similarity_cache: 相似度缓存
        end_time: 结束时间，用于检查是否超时
        early_stop_threshold: 早期终止阈值，如果相似度已经超过此值，可以提前返回
    
    返回:
        float: 相似度，范围[0,1]，值越大表示越相似
    """
    # 检查是否超时
    if end_time is not None and time.time() >= end_time:
        return 0.0
    
    # 更新缓存统计
    cache_stats['total_calls'] += 1
    
    # 获取路径长度
    n = len(path)
    
    # 检查是否需要清理缓存 - 在高频调用函数中添加检查点
    current_cache_size = len(segment_cache)
    dynamic_cache_size = 1000  # 默认缓存大小
    
    # 根据问题规模调整缓存大小
    if n > 100:
        dynamic_cache_size = 800  # 大规模问题使用较小的缓存
    elif n < 50:
        dynamic_cache_size = 1200  # 小规模问题可以使用较大的缓存
    
    # 如果缓存已经很大，立即检查并可能清理
    if current_cache_size > dynamic_cache_size * 0.9 or cache_stats['total_calls'] % 100 == 0:
        force_cleanup = current_cache_size > dynamic_cache_size * 0.95
        limit_cache_size(max_size=dynamic_cache_size, force_cleanup=force_cleanup)
    
    # 如果提供了缓存，尝试从缓存获取结果
    if similarity_cache is not None:
        cache_key = get_cache_key(path, sol)
        if cache_key in similarity_cache:
            cache_stats['hits'] += 1
            return similarity_cache[cache_key]
    
    # 也可以尝试从全局缓存中获取
    global_cache_key = get_cache_key(path, sol)
    if global_cache_key in segment_cache and 'similarity' in segment_cache[global_cache_key]:
        # 更新缓存使用时间和命中统计
        cache_usage_time[global_cache_key] = time.time()
        cache_stats['hits'] += 1
        
        # 如果提供了局部缓存，也更新局部缓存
        if similarity_cache is not None:
            similarity_cache[cache_key] = segment_cache[global_cache_key]['similarity']
        return segment_cache[global_cache_key]['similarity']
    
    n = len(path)
    # 计算共享边的数量作为相似度度量
    shared_edges = 0
    required_edges = int(early_stop_threshold * n)  # 达到早期终止所需的边数
    
    # 构建path的位置映射，加速查找
    path_list = path.tolist() if isinstance(path, np.ndarray) else path
    path_pos = {city: idx for idx, city in enumerate(path_list)}
    
    # 优化：预先检查两个解是否包含相同的城市集合
    # 如果城市集合不同，相似度必然较低
    # 检查是否超时
    if end_time is not None and time.time() >= end_time:
        return 0.0
        
    path_cities = set(path_list)
    sol_cities = set(sol)
    
    # 再次检查是否超时，集合操作可能耗时
    if end_time is not None and time.time() >= end_time:
        return 0.0
        
    if path_cities != sol_cities:
        # 城市集合不同，相似度可能较低，但仍需计算
        # 可以考虑使用城市集合的Jaccard相似度作为初步估计
        # 检查是否超时
        if end_time is not None and time.time() >= end_time:
            return 0.0
            
        jaccard_sim = len(path_cities.intersection(sol_cities)) / len(path_cities.union(sol_cities))
        
        # 检查是否超时
        if end_time is not None and time.time() >= end_time:
            return 0.0
            
        if jaccard_sim < 0.5:  # 如果城市集合差异很大，可以提前返回低相似度
            similarity = jaccard_sim * 0.5  # 粗略估计
            
            # 存入缓存
            if similarity_cache is not None:
                similarity_cache[cache_key] = similarity
            if global_cache_key not in segment_cache:
                segment_cache[global_cache_key] = {}
            segment_cache[global_cache_key]['similarity'] = similarity
            cache_usage_time[global_cache_key] = time.time()
            
            return similarity
    
    # 批处理方式计算相似度，减少超时检查频率
    batch_size = 1  # 每批处理的城市数量，从2减少到1，最大程度提高超时检测粒度
    for i in range(0, n, batch_size):
        # 每批次检查一次是否超时
        if end_time is not None and time.time() >= end_time:
            # 如果超时，返回已计算的相似度
            return shared_edges / n if n > 0 else 0.0
        
        # 处理当前批次
        end_idx = min(i + batch_size, n)
        for j in range(i, end_idx):
            # 在每个城市处理时也检查超时，进一步提高检测粒度
            if end_time is not None and time.time() >= end_time:
                return shared_edges / n if n > 0 else 0.0
                
            city = sol[j]
            next_city = sol[(j+1)%n]
            if city in path_pos:
                idx = path_pos[city]
                if next_city == path_list[(idx+1)%n]:
                    shared_edges += 1
                    
                    # 早期终止条件：如果已经确定相似度很高，可以提前返回
                    if shared_edges >= required_edges:
                        # 已经达到早期终止阈值，可以提前返回
                        similarity = shared_edges / n
                        
                        # 存入缓存
                        if similarity_cache is not None:
                            similarity_cache[cache_key] = similarity
                        if global_cache_key not in segment_cache:
                            segment_cache[global_cache_key] = {}
                        segment_cache[global_cache_key]['similarity'] = similarity
                        cache_usage_time[global_cache_key] = time.time()
                        
                        return similarity
    
    similarity = shared_edges / n  # 归一化相似度
    
    # 如果提供了缓存，将结果存入缓存
    if similarity_cache is not None:
        similarity_cache[cache_key] = similarity
    
    # 同时存入全局缓存
    if global_cache_key not in segment_cache:
        segment_cache[global_cache_key] = {}
    segment_cache[global_cache_key]['similarity'] = similarity
    cache_usage_time[global_cache_key] = time.time()
    
    return similarity

def pattern_based_perturbation(path, known_solutions, progress, end_time=None):
    """
    基于已知解模式的扰动：分析已知最优解的共同模式和差异，有针对性地进行扰动
    优化版：使用缓存减少重复计算
    """
    time_tracker.start_component_timer('pattern_based_perturbation')
    n = len(path)
    
    # 如果已知解数量不足，退化为自适应随机扰动
    if len(known_solutions) < 2:
        time_tracker.end_component_timer('pattern_based_perturbation')
        return adaptive_random_perturbation(path, progress, end_time)
    
    # 随机选择两个已知解进行分析
    sol1, sol2 = random.sample(known_solutions, 2)
    
    # 找出两个解的共同段和差异段，使用缓存减少计算
    # 传递end_time参数给find_common_segments和find_different_segments
    common_segments = find_common_segments(sol1, sol2, end_time=end_time)
    # 检查是否在find_common_segments中超时
    if end_time is not None and time.time() >= end_time:
        time_tracker.end_component_timer('pattern_based_perturbation')
        return path
        
    different_segments = find_different_segments(sol1, sol2, end_time=end_time)
    # 检查是否在find_different_segments中超时
    if end_time is not None and time.time() >= end_time:
        time_tracker.end_component_timer('pattern_based_perturbation')
        return path
    
    if not different_segments:
        # 如果没有明显差异，退化为自适应随机扰动
        time_tracker.end_component_timer('pattern_based_perturbation')
        # 直接调用adaptive_random_perturbation而不是递归调用topology_aware_perturbation
        return adaptive_random_perturbation(path, progress, end_time)
    
    # 根据进度决定扰动策略
    if progress < 0.3:
        # 早期：更多随机性，尝试多个差异段
        num_segments = min(3, len(different_segments))
        segments_to_perturb = random.sample(different_segments, num_segments) if len(different_segments) >= num_segments else different_segments
    elif progress < 0.7:
        # 中期：更有针对性，选择1-2个差异段
        num_segments = min(2, len(different_segments))
        segments_to_perturb = random.sample(different_segments, num_segments) if len(different_segments) >= num_segments else different_segments
    else:
        # 后期：更精细化，只选择一个差异段进行精确扰动
        segments_to_perturb = [random.choice(different_segments)]
    
    # 对选定的差异段进行扰动
    for segment in segments_to_perturb:
        # 检查是否超时
        if end_time is not None and time.time() >= end_time:
            break
        start, end = segment
        segment_length = (end - start) % n
        
        # 根据进度和随机性决定扰动方式
        perturbation_type = random.choices(
            ['replace_with_other', 'shuffle', 'reverse', 'partial_shuffle'],
            weights=[0.4, 0.2, 0.2, 0.2],
            k=1
        )[0]
        
        if perturbation_type == 'replace_with_other':
            # 检查是否超时
            if end_time is not None and time.time() >= end_time:
                break
                
            # 方式1：用另一个解中对应位置的段替换
            other_sol = sol1 if np.array_equal(path, sol2) else sol2
            # 找到对应的城市
            cities = set(path[start:end] if start < end else np.concatenate([path[start:], path[:end]]))
            
            # 检查是否超时
            if end_time is not None and time.time() >= end_time:
                break
                
            # 在other_sol中找到这些城市的位置
            indices = []
            for i, city in enumerate(other_sol):
                # 每处理几个城市检查一次是否超时
                if end_time is not None and i % 2 == 0 and time.time() >= end_time:
                    break
                if city in cities:
                    indices.append(i)
            
            # 检查是否超时
            if end_time is not None and time.time() >= end_time:
                break
                
            if indices and len(indices) >= 2:
                # 按照other_sol中的顺序重排这段
                new_segment = []
                for i, city in enumerate(other_sol):
                    # 每处理几个城市检查一次是否超时
                    if end_time is not None and i % 2 == 0 and time.time() >= end_time:
                        break
                    if city in cities:
                        new_segment.append(city)
                
                # 检查是否超时
                if end_time is not None and time.time() >= end_time:
                    break
                    
                # 确保长度匹配
                if len(new_segment) > segment_length:
                    new_segment = new_segment[:segment_length]
                
                # 确保所有原始城市都包含在新段中
                orig_segment = path[start:end].copy() if start < end else np.concatenate([path[start:], path[:end]])
                orig_cities = set(orig_segment)
                new_cities = set(new_segment)
                missing_cities = orig_cities - new_cities
                
                # 检查是否超时
                if end_time is not None and time.time() >= end_time:
                    break
                    
                # 如果有缺失的城市，将它们添加到新段中
                if missing_cities:
                    for i, city in enumerate(orig_segment):
                        # 每处理几个城市检查一次是否超时
                        if end_time is not None and i % 1 == 0 and time.time() >= end_time:
                            break
                        if city in missing_cities and city not in new_segment:
                            new_segment.append(city)
                
                # 检查是否超时
                if end_time is not None and time.time() >= end_time:
                    break
                    
                # 应用新段
                if start < end:
                    path[start:end] = new_segment
                else:
                    # 处理环绕情况
                    path[start:] = new_segment[:n-start]
                    path[:end] = new_segment[n-start:]
        
        elif perturbation_type == 'shuffle':
            # 方式2：随机重排这段
            if start < end:
                segment_cities = path[start:end].copy()
                random.shuffle(segment_cities)
                path[start:end] = segment_cities
            else:
                # 处理环绕情况
                temp = np.concatenate([path[start:], path[:end]])
                np.random.shuffle(temp)
                path[start:] = temp[:n-start]
                path[:end] = temp[n-start:]
        
        elif perturbation_type == 'reverse':
            # 方式3：反转这段
            if start < end:
                path[start:end] = path[start:end][::-1]
            else:
                # 处理环绕情况
                temp = np.concatenate([path[start:], path[:end]])
                temp = temp[::-1]  # 反转
                path[start:] = temp[:n-start]
                path[:end] = temp[n-start:]
        
        elif perturbation_type == 'partial_shuffle':
            # 方式4：部分打乱（保留一些相邻关系）
            if start < end:
                segment_cities = path[start:end].copy()
                # 只打乱一半的城市
                shuffle_size = max(2, len(segment_cities) // 2)
                shuffle_start = random.randint(0, len(segment_cities) - shuffle_size)
                sub_segment = segment_cities[shuffle_start:shuffle_start+shuffle_size]
                random.shuffle(sub_segment)
                segment_cities[shuffle_start:shuffle_start+shuffle_size] = sub_segment
                path[start:end] = segment_cities
            else:
                # 处理环绕情况
                temp = np.concatenate([path[start:], path[:end]])
                shuffle_size = max(2, len(temp) // 2)
                shuffle_start = random.randint(0, len(temp) - shuffle_size)
                sub_segment = temp[shuffle_start:shuffle_start+shuffle_size].copy()
                np.random.shuffle(sub_segment)
                temp[shuffle_start:shuffle_start+shuffle_size] = sub_segment
                path[start:] = temp[:n-start]
                path[:end] = temp[n-start:]
    
    # 如果有多个已知解，智能检测当前解是否与任何已知解过于相似
    # 使用自适应策略，根据问题规模和进度调整检查频率
    check_probability = 0.1 + (0.2 * (1 - progress))  # 早期更频繁检查，后期减少
    check_probability = min(0.3, max(0.1, check_probability))  # 限制在0.1-0.3之间
    
    # 根据问题规模调整检查概率 - 大规模问题降低检查频率
    if n > 100:
        check_probability *= 0.7  # 大规模问题降低检查频率
    
    # 检查是否超时 - 在进行相似度计算前检查
    if end_time is not None and time.time() >= end_time:
        time_tracker.end_component_timer('pattern_based_perturbation')
        return path
    
    if len(known_solutions) > 2 and random.random() < check_probability:
        # 检查是否超时
        if end_time is not None and time.time() >= end_time:
            time_tracker.end_component_timer('pattern_based_perturbation')
            return path
            
        # 使用缓存存储相似度计算结果
        similarity_cache = {}
        
        # 智能选择要检查的解 - 优先选择与当前解可能相似的解
        # 这里使用一个简单的启发式：选择一些最近更新的解和一些随机解
        
        # 根据问题规模动态调整检查数量 - 大规模问题减少检查数量
        if n > 100:
            check_count = min(2, len(known_solutions))  # 大规模问题减少检查数量
        else:
            check_count = min(3, len(known_solutions))  # 减少检查数量，提高效率
        
        # 检查是否超时 - 在抽样前检查
        if end_time is not None and time.time() >= end_time:
            time_tracker.end_component_timer('pattern_based_perturbation')
            return path
            
        # 计算与选定解的相似度
        similarities = []
        
        # 检查是否超时 - 在随机抽样前检查
        if end_time is not None and time.time() >= end_time:
            time_tracker.end_component_timer('pattern_based_perturbation')
            return path
            
        try:
            check_solutions = random.sample(known_solutions, check_count)
        except ValueError as e:
            # 处理可能的ValueError: Sample larger than population or is negative
            check_solutions = known_solutions[:min(check_count, len(known_solutions))]
        
        # 使用早期终止阈值进行相似度计算
        for i, sol in enumerate(check_solutions):
            # 每处理一个解前检查是否超时，提高检测粒度
            if end_time is not None and time.time() >= end_time:
                # 如果超时但已有一些相似度结果，可以继续使用
                if similarities:
                    break
                else:
                    # 如果没有任何结果，直接返回原路径
                    time_tracker.end_component_timer('pattern_based_perturbation')
                    return path
                    
            # 使用早期终止阈值0.85加速计算
            try:
                similarity = calculate_similarity(path, sol, similarity_cache, end_time, early_stop_threshold=0.85)
                similarities.append(similarity)
                
                # 如果发现一个高相似度解，立即停止检查其他解
                if similarity > 0.85:
                    break
            except Exception as e:
                # 捕获计算相似度时可能出现的异常，确保不会中断主流程
                print(f"计算相似度时出错: {str(e)}")
                # 检查是否超时
                if end_time is not None and time.time() >= end_time:
                    # 如果已经超时，直接返回
                    time_tracker.end_component_timer('pattern_based_perturbation')
                    return path
        
        # 如果与任何已知解相似度过高，进行更智能的扰动
        similarity_threshold = 0.8 - (0.1 * progress)  # 随着搜索进展提高阈值
        similarity_threshold = max(0.7, min(0.85, similarity_threshold))  # 限制在0.7-0.85之间
        
        # 检查是否有有效的相似度结果
        if similarities and max(similarities) > similarity_threshold:
            # 检查是否超时
            if end_time is not None and time.time() >= end_time:
                time_tracker.end_component_timer('pattern_based_perturbation')
                return path
            
            # 根据问题规模和进度调整扰动强度
            if progress < 0.3:
                # 早期：较大扰动
                segment_count = 2
                segment_size_ratio = 0.15  # 扰动15%的路径
            elif progress < 0.7:
                # 中期：中等扰动
                segment_count = 1
                segment_size_ratio = 0.1   # 扰动10%的路径
            else:
                # 后期：精细扰动
                segment_count = 1
                segment_size_ratio = 0.05  # 扰动5%的路径
            
            # 大规模问题减少扰动次数和强度
            if n > 100:
                segment_count = min(segment_count, 1)  # 最多1次扰动
                segment_size_ratio *= 0.7  # 减少扰动强度
            
            # 执行多段扰动
            for i in range(segment_count):
                # 检查是否超时 - 每次扰动前检查
                if end_time is not None and time.time() >= end_time:
                    break
                    
                try:
                    # 计算扰动段大小
                    segment_size = max(3, int(n * segment_size_ratio))
                    
                    # 检查是否超时 - 在随机选择前检查
                    if end_time is not None and time.time() >= end_time:
                        break
                        
                    extra_start = random.randint(0, n-1)
                    extra_end = (extra_start + segment_size) % n
                    
                    # 检查是否超时 - 在执行扰动前检查
                    if end_time is not None and time.time() >= end_time:
                        break
                    
                    if extra_start < extra_end:
                        segment = path[extra_start:extra_end].copy()
                        random.shuffle(segment)
                        path[extra_start:extra_end] = segment
                    else:
                        # 处理环绕情况
                        # 检查是否超时 - 在处理环绕情况前检查
                        if end_time is not None and time.time() >= end_time:
                            break
                            
                        temp = np.concatenate([path[extra_start:], path[:extra_end]])
                        np.random.shuffle(temp)
                        path[extra_start:] = temp[:n-extra_start]
                        path[:extra_end] = temp[n-extra_start:]
                except Exception as e:
                    # 捕获扰动过程中可能出现的异常，确保不会中断主流程
                    print(f"执行扰动时出错: {str(e)}")
                    # 检查是否超时
                    if end_time is not None and time.time() >= end_time:
                        break
    
 
    
    # 动态调整缓存大小限制
    dynamic_cache_size = 1000
    if n > 100:
        dynamic_cache_size = 800  # 大规模问题使用较小的缓存
    elif n < 50:
        dynamic_cache_size = 1200  # 小规模问题可以使用较大的缓存
    
    # 确定是否需要检查缓存 - 不再使用随机概率
    current_cache_size = len(segment_cache)
    
    # 基于缓存大小的阈值检查 - 随着缓存增长，检查频率提高
    size_threshold_ratio = 0.7  # 当缓存达到70%容量时开始检查
    if n > 100:  # 大规模问题降低阈值，更积极清理
        size_threshold_ratio = 0.6
    
    # 确定是否需要检查缓存 - 基于多个条件
    cache_check_needed = (
        # 1. 缓存大小超过阈值
        current_cache_size > dynamic_cache_size * size_threshold_ratio or
        # 2. 缓存调用次数达到检查间隔
        cache_stats['total_calls'] % max(5, int(50 / (1 + current_cache_size / dynamic_cache_size))) == 0 or
        # 3. 距离上次清理已超过一定时间
        (time.time() - cache_stats['last_cleanup']) > 300  # 5分钟
    )
    
    # 根据缓存大小决定是否强制清理
    force_cleanup = current_cache_size > dynamic_cache_size * 0.85
    
    # 大规模问题或缓存接近上限时，强制清理
    if n > 100 and current_cache_size > dynamic_cache_size * 0.8:
        force_cleanup = True
        cache_check_needed = True
    
    if cache_check_needed:
        limit_cache_size(max_size=dynamic_cache_size, force_cleanup=force_cleanup)
    
    time_tracker.end_component_timer('pattern_based_perturbation')
    return path


def clear_segment_cache():
    """
    清空段缓存，在内存压力大时调用
    """
    global segment_cache
    segment_cache = {}

# 添加缓存使用时间记录和缓存统计信息
cache_usage_time = {}
cache_stats = {
    'total_calls': 0,      # 缓存调用总次数
    'hits': 0,             # 缓存命中次数
    'last_cleanup': time.time(),  # 上次清理时间
    'max_size_reached': 0  # 历史最大缓存大小
}

def limit_cache_size(max_size=1000, force_cleanup=False):
    """
    限制缓存大小，当缓存项超过max_size时，删除最近最少使用的项
    实现改进的LRU（最近最少使用）缓存策略，并支持强制清理
    
    参数:
        max_size: 缓存最大条目数
        force_cleanup: 是否强制执行清理，即使未达到max_size
    
    返回:
        bool: 是否执行了清理
    """
    global segment_cache, cache_usage_time, cache_stats
    current_size = len(segment_cache)
    
    # 更新历史最大缓存大小统计
    if current_size > cache_stats['max_size_reached']:
        cache_stats['max_size_reached'] = current_size
        # 当达到新的最大值时，记录日志
        # print(f"缓存达到新的最大值: {current_size} 项")
    
    # 检查是否需要清理缓存 - 更智能的判断逻辑
    # 1. 缓存大小超过阈值 或 
    # 2. 强制清理 或 
    # 3. 距离上次清理已超过时间阈值（根据缓存大小动态调整）
    time_since_last_cleanup = time.time() - cache_stats['last_cleanup']
    
    # 根据缓存大小动态调整清理时间间隔 - 缓存越大，间隔越短
    cleanup_interval = 1800  # 默认30分钟
    if current_size > max_size * 0.8:
        cleanup_interval = 900   # 15分钟
    elif current_size > max_size * 0.5:
        cleanup_interval = 1200  # 20分钟
    
    cleanup_needed = (
        current_size > max_size or 
        force_cleanup or 
        time_since_last_cleanup > cleanup_interval
    )
    
    if cleanup_needed:
        # 实现改进的LRU缓存策略
        # 按照最后使用时间排序
        sorted_keys = sorted(cache_usage_time.items(), key=lambda x: x[1])
        
        # 确定要保留的缓存项数量 - 更智能的清理策略
        # 根据缓存大小和强制清理标志动态调整保留比例
        if current_size > max_size * 1.2:  # 严重超出
            keep_ratio = 0.6  # 只保留60%
        elif current_size > max_size:
            keep_ratio = 0.7  # 保留70%
        elif force_cleanup and current_size > max_size * 0.9:
            keep_ratio = 0.8  # 接近阈值且强制清理，保留80%
        elif force_cleanup:
            keep_ratio = 0.9  # 强制清理但未接近阈值，保留90%
        else:  # 定时清理
            keep_ratio = 0.85
        
        # 计算要保留的缓存项数量
        target_size = int(max_size * 0.8)  # 目标是保持在最大容量的80%左右
        keep_size = min(target_size, int(current_size * keep_ratio))
        keep_size = min(keep_size, current_size)  # 确保不超过当前大小
        
        # 确定要删除的键
        keys_to_remove = [k for k, _ in sorted_keys[:current_size - keep_size]]
        
        # 批量删除最近最少使用的缓存项
        removed_count = 0
        for key in keys_to_remove:
            if key in segment_cache:
                del segment_cache[key]
                removed_count += 1
            if key in cache_usage_time:
                del cache_usage_time[key]
        
        # 更新最后清理时间
        cache_stats['last_cleanup'] = time.time()
        
        # 更新缓存清理统计
        if 'cleanup_count' not in cache_stats:
            cache_stats['cleanup_count'] = 0
            cache_stats['total_removed'] = 0
        
        cache_stats['cleanup_count'] += 1
        cache_stats['total_removed'] += removed_count
        
        # # 打印详细的缓存清理信息
        # print(f"缓存清理 #{cache_stats['cleanup_count']}: 从 {current_size} 减少到 {len(segment_cache)} 项")
        # print(f"  - 删除: {removed_count} 项 (保留率: {keep_ratio:.2f})")
        # print(f"  - 触发条件: {'大小超限' if current_size > max_size else '强制清理' if force_cleanup else '定时清理'}")
        # print(f"  - 缓存统计: 总调用 {cache_stats['total_calls']}, 命中率 {cache_stats['hits']/max(1, cache_stats['total_calls']):.2f}")
        
        # 如果有logging模块，记录到日志
        # try:
        #     import logging
        #     logging.info(f"缓存清理: 从 {current_size} 减少到 {len(segment_cache)} 项 (保留率: {keep_ratio:.2f})")
        #     logging.info(f"缓存统计: 总调用 {cache_stats['total_calls']}, 命中率 {cache_stats['hits']/max(1, cache_stats['total_calls']):.2f}")
        # except ImportError:
        #     pass
        
        return True  # 返回是否执行了清理
    
    return False  # 未执行清理

# 以下是其他扰动函数的声明，实际实现需要从原文件复制
def adaptive_random_perturbation(path, progress, end_time=None):
    """
    自适应随机扰动：根据搜索进度调整扰动强度
    """
    time_tracker.start_component_timer('adaptive_random_perturbation')
    n = len(path)
    
    # 根据进度调整扰动强度
    if progress < 0.3:
        # 早期：大范围扰动
        num_segments = max(1, n // 10)
        segment_size = max(3, n // 5)
    elif progress < 0.7:
        # 中期：中等范围扰动
        num_segments = max(2, n // 8)
        segment_size = max(3, n // 8)
    else:
        # 后期：小范围扰动
        num_segments = max(3, n // 6)
        segment_size = max(2, n // 10)
    
    # 随机选择几段进行扰动
    for _ in range(num_segments):
        # 检查是否超时
        if end_time is not None and time.time() >= end_time:
            break
            
        start = random.randint(0, n - 1)
        size = min(random.randint(2, segment_size), n - 1)
        end = (start + size) % n
        
        # 随机选择扰动操作
        op = random.choice(['shuffle', 'reverse', 'swap'])
        
        if op == 'shuffle':
            # 打乱这段路径
            if start < end:
                segment = path[start:end].copy()
                random.shuffle(segment)
                path[start:end] = segment
            else:
                # 处理环绕情况
                temp = np.concatenate([path[start:], path[:end]])
                np.random.shuffle(temp)
                path[start:] = temp[:n-start]
                path[:end] = temp[n-start:]
        
        elif op == 'reverse':
            # 反转这段路径
            if start < end:
                path[start:end] = path[start:end][::-1]
            else:
                # 处理环绕情况
                temp = np.concatenate([path[start:], path[:end]])
                temp = temp[::-1]  # 反转
                path[start:] = temp[:n-start]
                path[:end] = temp[n-start:]
        
        elif op == 'swap':
            # 交换两个随机位置的城市
            pos1 = random.randint(0, n - 1)
            pos2 = random.randint(0, n - 1)
            while pos1 == pos2:  # 确保两个位置不同
                pos2 = random.randint(0, n - 1)
            path[pos1], path[pos2] = path[pos2], path[pos1]
    
    time_tracker.end_component_timer('adaptive_random_perturbation')
    return path

def segment_preservation_perturbation(path, dis_matrix, progress, end_time=None):
    """
    段保留扰动：保留路径中的某些关键段，只扰动其他部分
    """
    time_tracker.start_component_timer('segment_preservation_perturbation')
    n = len(path)
    
    # 确定要保留的段数和每段的长度
    if progress < 0.3:
        num_segments = 2
        min_seg_length = max(3, n // 10)
    elif progress < 0.7:
        num_segments = 3
        min_seg_length = max(3, n // 12)
    else:
        num_segments = 4
        min_seg_length = max(2, n // 15)
    
    # 如果有距离矩阵，尝试找出成本较低的段保留
    preserved_segments = []
    if dis_matrix is not None:
        # 计算每条边的成本
        edge_costs = []
        for i in range(n):
            next_i = (i + 1) % n
            cost = dis_matrix[path[i], path[next_i]]
            edge_costs.append((i, cost))
        
        # 按成本排序（升序）
        edge_costs.sort(key=lambda x: x[1])
        
        # 选择成本最低的几条边作为段的起点
        start_points = [edge_costs[i][0] for i in range(min(num_segments * 2, len(edge_costs)))]
        start_points.sort()
        
        # 尝试找出连续的低成本边形成的段
        for i in range(len(start_points) - 1):
            start = start_points[i]
            potential_end = start_points[i + 1]
            
            # 检查这段是否足够短（避免保留太长的段）
            segment_length = (potential_end - start) % n
            if min_seg_length <= segment_length <= min_seg_length * 2:
                # 计算这段的平均成本
                segment_cost = 0
                for j in range(segment_length):
                    idx = (start + j) % n
                    next_idx = (idx + 1) % n
                    segment_cost += dis_matrix[path[idx], path[next_idx]]
                segment_cost /= segment_length
                
                # 如果平均成本足够低，保留这段
                if segment_cost < sum([x[1] for x in edge_costs]) / len(edge_costs):
                    preserved_segments.append((start, potential_end))
                    if len(preserved_segments) >= num_segments:
                        break
    
    # 如果没有找到足够的段，使用单边低成本方法
    if len(preserved_segments) < num_segments:
        # 重新计算每条边的成本并排序
        if dis_matrix is not None:
            edge_costs = []
            for i in range(n):
                next_i = (i + 1) % n
                cost = dis_matrix[path[i], path[next_i]]
                edge_costs.append((i, cost))
            edge_costs.sort(key=lambda x: x[1])
            
            # 选择成本最低的几条边作为段的起点
            low_cost_edges = edge_costs[:min(num_segments * 3, len(edge_costs))]
            
            # 尝试从这些低成本边开始构建段
            for i, (edge_start, _) in enumerate(low_cost_edges):
                if len(preserved_segments) >= num_segments:
                    break
                    
                # 检查是否与已有段重叠
                overlap = False
                potential_end = (edge_start + min_seg_length) % n
                for seg_start, seg_end in preserved_segments:
                    # 检查是否超时 - 在调用segments_overlap前检查
                    if end_time is not None and time.time() >= end_time:
                        overlap = True  # 如果超时，假设重叠以跳过添加新段
                        break
                    # 检查新段是否与现有段重叠
                    if segments_overlap(edge_start, potential_end, seg_start, seg_end, n, end_time):
                        overlap = True
                        break
                
                if not overlap:
                    preserved_segments.append((edge_start, potential_end))
    
    # 如果仍然没有找到足够的段，随机选择，但添加最大尝试次数限制
    max_attempts = min(100, n * 5)  # 设置最大尝试次数，避免无限循环
    attempts = 0
    original_min_seg_length = min_seg_length  # 保存原始段长度，以便后续可能需要调整
    
    while len(preserved_segments) < num_segments and attempts < max_attempts:
        # 检查是否超时
        if end_time is not None and time.time() >= end_time:
            break
            
        start = random.randint(0, n - 1)
        end = (start + min_seg_length) % n
        
        # 检查是否与已有段重叠
        overlap = False
        for seg_start, seg_end in preserved_segments:
            # 检查是否超时
            if end_time is not None and time.time() >= end_time:
                overlap = True  # 如果超时，假设重叠以跳过添加新段
                break
            if segments_overlap(start, end, seg_start, seg_end, n, end_time):
                overlap = True
                break
        
        if not overlap:
            preserved_segments.append((start, end))
        
        attempts += 1
        
        # 如果尝试次数达到一定阈值但仍未找到足够段，则调整策略
        if attempts % 20 == 0 and len(preserved_segments) < num_segments:
            # 策略1：减小段长度要求
            if min_seg_length > 2:
                min_seg_length = max(2, min_seg_length - 1)
                
            # 策略2：如果段长度已经最小，且已经尝试了很多次，减少需要的段数
            if min_seg_length == 2 and attempts >= max_attempts // 2 and num_segments > 1:
                num_segments -= 1
    
    # 如果尝试了最大次数后仍未找到足够的段，使用退化策略
    if len(preserved_segments) < num_segments:
        # 记录日志
        logging.warning(f"无法找到足够的不重叠段 (找到 {len(preserved_segments)}/{num_segments})，使用退化策略")
        
        # 退化策略：如果至少找到了一个段，就使用已找到的段
        # 如果一个段都没找到，则保留一个随机段
        if len(preserved_segments) == 0:
            start = random.randint(0, n - 1)
            end = (start + original_min_seg_length) % n
            preserved_segments.append((start, end))
            logging.info("退化策略：添加一个随机段")
    
    # 创建一个掩码，标记哪些位置是保留的
    preserved_mask = [False] * n
    for start, end in preserved_segments:
        if start <= end:
            for i in range(start, end):
                preserved_mask[i] = True
        else:  # 处理环绕情况
            for i in range(start, n):
                preserved_mask[i] = True
            for i in range(0, end):
                preserved_mask[i] = True
    
    # 找出可以扰动的段
    perturbable_segments = []
    start = None
    for i in range(n + 1):  # 额外一次迭代处理环绕情况
        idx = i % n
        if not preserved_mask[idx] and start is None:
            start = idx
        elif (preserved_mask[idx] or i == n) and start is not None:
            end = idx
            perturbable_segments.append((start, end))
            start = None
    
    # 对每个可扰动段进行随机扰动
    for start, end in perturbable_segments:
        # 检查是否超时
        if end_time is not None and time.time() >= end_time:
            break
        if (end - start) % n >= 2:  # 至少需要2个城市才能扰动
            # 检查是否超时 - 在执行扰动操作前再次检查
            if end_time is not None and time.time() >= end_time:
                break
                
            # 根据进度选择不同的扰动方式
            if progress < 0.3:
                # 早期：完全随机打乱
                if start < end:
                    segment = path[start:end].copy()
                    random.shuffle(segment)
                    path[start:end] = segment
                else:  # 处理环绕情况
                    temp = np.concatenate([path[start:], path[:end]])
                    np.random.shuffle(temp)
                    path[start:] = temp[:n-start]
                    path[:end] = temp[n-start:]
            elif progress < 0.7:
                # 中期：随机选择打乱或反转
                op = random.choice(['shuffle', 'reverse'])
                if start < end:
                    segment = path[start:end].copy()
                    if op == 'shuffle':
                        random.shuffle(segment)
                    else:  # reverse
                        segment = segment[::-1]
                    path[start:end] = segment
                else:  # 处理环绕情况
                    temp = np.concatenate([path[start:], path[:end]])
                    if op == 'shuffle':
                        np.random.shuffle(temp)
                    else:  # reverse
                        temp = temp[::-1]
                    path[start:] = temp[:n-start]
                    path[:end] = temp[n-start:]
            else:
                # 后期：更精细化扰动，只打乱一部分
                if start < end:
                    segment = path[start:end].copy()
                    # 只打乱一半的城市
                    shuffle_size = max(2, (end - start) // 2)
                    shuffle_start = random.randint(start, end - shuffle_size)
                    # 检查是否超时 - 在执行细粒度扰动前再次检查
                    if end_time is not None and time.time() >= end_time:
                        break
                    sub_segment = path[shuffle_start:shuffle_start+shuffle_size].copy()
                    random.shuffle(sub_segment)
                    path[shuffle_start:shuffle_start+shuffle_size] = sub_segment
                else:  # 处理环绕情况
                    # 检查是否超时 - 在处理环绕情况前检查
                    if end_time is not None and time.time() >= end_time:
                        break
                    # 简化处理：只反转这段
                    temp = np.concatenate([path[start:], path[:end]])
                    temp = temp[::-1]
                    path[start:] = temp[:n-start]
                    path[:end] = temp[n-start:]
    
    time_tracker.end_component_timer('segment_preservation_perturbation')
    return path

def critical_edge_perturbation(path, dis_matrix, progress, end_time=None):
    """
    关键边扰动：识别并扰动路径中的关键边（成本高的边）
    """
    time_tracker.start_component_timer('critical_edge_perturbation')
    n = len(path)
    
    if dis_matrix is None:
        # 如果没有距离矩阵，退化为自适应随机扰动
        return adaptive_random_perturbation(path, progress, end_time)
    
    # 计算每条边的成本
    edge_costs = []
    for i in range(n):
        next_i = (i + 1) % n
        cost = dis_matrix[path[i], path[next_i]]
        edge_costs.append((i, cost))
    
    # 按成本降序排序
    edge_costs.sort(key=lambda x: x[1], reverse=True)
    
    # 根据进度决定要扰动的边数
    num_edges = max(1, int(n * (0.1 + 0.1 * (1 - progress))))
    
    # 选择成本最高的几条边
    high_cost_edges = [edge_costs[i][0] for i in range(min(num_edges, len(edge_costs)))]
    
    # 对每条高成本边进行扰动
    for edge_start in high_cost_edges:
        # 检查是否超时
        if end_time is not None and time.time() >= end_time:
            break
        edge_end = (edge_start + 1) % n
        
        # 随机选择扰动方式
        perturbation_type = random.choice(['reverse', 'relocate', 'swap'])
        
        if perturbation_type == 'reverse':
            # 反转包含这条边的一端路径
            segment_length = random.randint(3, max(3, n // 5))
            start = edge_start
            end = (edge_start + segment_length) % n
            
            # 处理环绕情况
            if start < end:
                path[start:end] = path[start:end][::-1]
            else:
                # 创建一个临时数组来处理环绕
                temp = np.concatenate([path[start:], path[:end]])
                temp = temp[::-1]  # 反转
                path[start:] = temp[:n-start]
                path[:end] = temp[n-start:]
        
        elif perturbation_type == 'relocate':
            # 检查是否超时 - 在执行relocate操作前检查
            if end_time is not None and time.time() >= end_time:
                break
                
            try:
                # 将这条边的终点重新定位到其他位置
                city = path[edge_end]
                # 从路径中移除这个城市
                path = np.delete(path, edge_end)
                
                # 检查是否超时 - 在选择新位置前检查
                if end_time is not None and time.time() >= end_time:
                    # 如果超时，恢复原路径
                    path = np.insert(path, edge_end, city)
                    break
                    
                # 随机选择一个新位置插入
                new_pos = random.randint(0, n - 2)
                path = np.insert(path, new_pos, city)
            except Exception as e:
                print(f"执行relocate操作时出错: {str(e)}")
                # 如果出错，检查是否超时
                if end_time is not None and time.time() >= end_time:
                    break
        
        elif perturbation_type == 'swap':
            # 检查是否超时 - 在执行swap操作前检查
            if end_time is not None and time.time() >= end_time:
                break
                
            try:
                # 交换这条边的两个端点与路径中的另外两个城市
                other_pos1 = random.randint(0, n - 1)
                
                # 检查是否超时 - 在循环前检查
                if end_time is not None and time.time() >= end_time:
                    break
                    
                # 确保选择不同的位置
                max_attempts = 10  # 限制尝试次数，避免无限循环
                attempt = 0
                while (other_pos1 == edge_start or other_pos1 == edge_end) and attempt < max_attempts:
                    other_pos1 = random.randint(0, n - 1)
                    attempt += 1
                    # 每次循环检查是否超时
                    if end_time is not None and time.time() >= end_time:
                        break
                
                # 检查是否超时 - 在选择第二个位置前检查
                if end_time is not None and time.time() >= end_time:
                    break
                    
                other_pos2 = (other_pos1 + 1) % n
                
                attempt = 0
                while (other_pos2 == edge_start or other_pos2 == edge_end) and attempt < max_attempts:
                    other_pos2 = (other_pos1 + 1) % n
                    attempt += 1
                    # 每次循环检查是否超时
                    if end_time is not None and time.time() >= end_time:
                        break
                
                # 检查是否超时 - 在执行交换前检查
                if end_time is not None and time.time() >= end_time:
                    break
                    
                # 交换
                path[edge_start], path[other_pos1] = path[other_pos1], path[edge_start]
                path[edge_end], path[other_pos2] = path[other_pos2], path[edge_end]
            except Exception as e:
                print(f"执行swap操作时出错: {str(e)}")
                # 如果出错，检查是否超时
                if end_time is not None and time.time() >= end_time:
                    break
    
    time_tracker.end_component_timer('critical_edge_perturbation')
    return path

def cluster_based_perturbation(path, dis_matrix, progress, end_time=None):
    """
    基于聚类的扰动：将路径分成几个聚类，在保持聚类内部结构的同时，改变聚类之间的连接
    """
    time_tracker.start_component_timer('cluster_based_perturbation')
    n = len(path)
    
    # 检查是否超时 - 在开始处理前检查
    if end_time is not None and time.time() >= end_time:
        time_tracker.end_component_timer('cluster_based_perturbation')
        return path
    
    # 确定聚类数量
    if progress < 0.3:
        num_clusters = max(2, n // 20)
    elif progress < 0.7:
        num_clusters = max(3, n // 15)
    else:
        num_clusters = max(4, n // 10)
    
    # 简单地将路径均匀分成几个聚类
    cluster_size = n // num_clusters
    clusters = []
    for i in range(0, n, cluster_size):
        # 每次处理一个聚类前检查是否超时，提高检测粒度
        if end_time is not None and time.time() >= end_time:
            # 如果超时，直接返回原路径
            time_tracker.end_component_timer('cluster_based_perturbation')
            return path
        end = min(i + cluster_size, n)
        clusters.append(path[i:end].copy())
    
    # 检查是否超时 - 在选择操作前检查
    if end_time is not None and time.time() >= end_time:
        time_tracker.end_component_timer('cluster_based_perturbation')
        return path
    
    # 随机选择扰动操作
    op = random.choice(['reorder', 'reverse', 'shuffle_internal'])
    
    if op == 'reorder':
        # 检查是否超时 - 在执行操作前检查
        if end_time is not None and time.time() >= end_time:
            time_tracker.end_component_timer('cluster_based_perturbation')
            return path
        # 重新排序聚类
        random.shuffle(clusters)
        # 重建路径
        new_path = np.concatenate(clusters)
        time_tracker.end_component_timer('cluster_based_perturbation')
        return new_path
    
    elif op == 'reverse':
        # 随机反转一些聚类
        for i in range(len(clusters)):
            # 每处理一个聚类前检查是否超时，提高检测粒度
            if end_time is not None and time.time() >= end_time:
                # 如果超时，使用当前已处理的结果
                break
            if random.random() < 0.3:
                clusters[i] = clusters[i][::-1]
        # 重建路径
        new_path = np.concatenate(clusters)
        time_tracker.end_component_timer('cluster_based_perturbation')
        return new_path
    
    elif op == 'shuffle_internal':
        # 在每个聚类内部随机打乱一小部分城市
        for i in range(len(clusters)):
            # 每处理一个聚类前检查是否超时，提高检测粒度
            if end_time is not None and time.time() >= end_time:
                # 如果超时，使用当前已处理的结果
                break
                
            try:
                cluster = clusters[i]
                if len(cluster) > 3:
                    # 检查是否超时 - 在执行细粒度操作前检查
                    if end_time is not None and time.time() >= end_time:
                        break
                        
                    # 随机选择一小段进行打乱
                    seg_size = max(2, len(cluster) // 3)
                    start = random.randint(0, len(cluster) - seg_size)
                    segment = cluster[start:start+seg_size].copy()
                    
                    # 检查是否超时 - 在执行shuffle前检查
                    if end_time is not None and time.time() >= end_time:
                        break
                        
                    random.shuffle(segment)
                    cluster[start:start+seg_size] = segment
            except Exception as e:
                print(f"处理聚类内部时出错: {str(e)}")
                # 如果出错，检查是否超时
                if end_time is not None and time.time() >= end_time:
                    break
        
        # 检查是否超时 - 在重建路径前检查
        if end_time is not None and time.time() >= end_time:
            # 如果超时，直接返回原路径
            time_tracker.end_component_timer('cluster_based_perturbation')
            return path
            
        try:
            # 重建路径
            new_path = np.concatenate(clusters)
            time_tracker.end_component_timer('cluster_based_perturbation')
            return new_path
        except Exception as e:
            print(f"重建路径时出错: {str(e)}")
            # 如果出错，返回原路径
            time_tracker.end_component_timer('cluster_based_perturbation')
            return path
    
    time_tracker.end_component_timer('cluster_based_perturbation')
    return path


def get_strategy_weights(progress):
    """
    根据搜索进度调整各策略的权重
    
    参数:
        progress: 多维度搜索进度，范围[0,1]
        
    返回:
        list: 四种策略的权重列表 [segment_preservation, critical_edge, cluster_based, adaptive_random]
    """
    # 根据多维度进度更精细地划分阶段
    if progress < 0.25:
        # 早期：更多随机性和基于聚类的扰动，促进多样化探索
        return [0.15, 0.15, 0.45, 0.25]
    elif progress < 0.5:
        # 早中期：增加结构保留，减少纯随机性
        return [0.25, 0.25, 0.3, 0.2]
    elif progress < 0.75:
        # 中后期：平衡各种策略，增加关键结构保留
        return [0.35, 0.35, 0.2, 0.1]
    else:
        # 后期：更注重保留关键段和关键边，减少随机扰动
        return [0.45, 0.45, 0.05, 0.05]

def topology_aware_perturbation(path, dis_matrix=None, known_solutions=None, progress=None, end_time=None):
    """
    拓扑感知扰动算法：专为多解TSP问题设计，能够在保持拓扑结构相似性的同时探索新的最优解
    优化版：使用缓存减少重复计算，更高效的超时检测
    
    参数:
        path: 当前路径（一维数组）
        dis_matrix: 距离矩阵
        known_solutions: 已知的最优解列表
        progress: 当前搜索进度，范围[0,1]
        end_time: 结束时间，用于检查是否超时
    
    返回:
        扰动后的新路径
    """
    # 记录开始时间
    perturb_start_time = time.time()
    time_tracker.start_component_timer('topology_aware_perturbation')
    
    if path is None or len(path) <= 3:
        # 如果路径无效，直接返回，不需要结束未开始的计时器
        time_tracker.end_component_timer('topology_aware_perturbation')
        return path.copy() if path is not None else None
    
    # 复制路径，避免修改原始路径
    path_copy = path.copy()
    n = len(path_copy)
    
    # 记录各个策略的时间
    strategy_times = {}
    
    # 如果没有提供进度，使用progress_calculator计算多维度进度
    if progress is None:
        from progress_calculator import calculate_progress
        progress = calculate_progress(
            current_iteration=None,  # 没有迭代信息时使用默认值
            problem_size=n,
            time_limit=None if end_time is None else (end_time - time.time()),
            end_time=end_time
        )
    
    # 根据已知解的模式选择扰动策略
    if known_solutions and len(known_solutions) >= 2 and random.random() < 0.7:
        strategy = 'pattern_based'
        strategy_start = time.time()
        result = pattern_based_perturbation(path_copy, known_solutions, progress, end_time)
        strategy_times[strategy] = time.time() - strategy_start
    else:
        # 选择扰动策略
        strategies = ['segment_preservation', 'critical_edge', 'cluster_based', 'adaptive_random']
        weights = get_strategy_weights(progress)
        strategy = random.choices(strategies, weights=weights, k=1)[0]
        strategy_start = time.time()
        if strategy == 'segment_preservation':
            result = segment_preservation_perturbation(path_copy, dis_matrix, progress, end_time)
        elif strategy == 'critical_edge':
            result = critical_edge_perturbation(path_copy, dis_matrix, progress, end_time)
        elif strategy == 'cluster_based':
            result = cluster_based_perturbation(path_copy, dis_matrix, progress, end_time)
        else:  # adaptive_random
            result = adaptive_random_perturbation(path_copy, progress, end_time)
        strategy_times[strategy] = time.time() - strategy_start
    
    # 计算总用时
    total_perturb_time = time.time() - perturb_start_time
    
    # 打印时间统计信息
    # logging.info(f"拓扑感知扰动用时: {total_perturb_time:.4f}秒，使用策略: {strategy}")
    # for s, t in strategy_times.items():
    #     percentage = (t / total_perturb_time) * 100 if total_perturb_time > 0 else 0
    #     print(f"  - {s}: {t:.4f}秒 ({percentage:.1f}%)")
    
    time_tracker.end_component_timer('topology_aware_perturbation')
    return result