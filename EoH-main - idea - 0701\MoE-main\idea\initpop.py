import numpy as np
import random
import logging

# 获取模块的日志记录器
logger = logging.getLogger(__name__)

class INIT:
    """
    初始化种群的类
    """
    
    @staticmethod
    def random_init(n, pop_size):
        """
        随机初始化种群
        
        参数:
            n (int): 城市数量
            pop_size (int): 种群大小
            
        返回:
            list: 初始化的种群列表
        """
        logger.info(f"随机初始化种群，城市数量: {n}, 种群大小: {pop_size}")
        population = []
        
        for _ in range(pop_size):
            # 生成随机排列
            tour = np.array(random.sample(range(n), n), dtype=np.int64)
            
            # 创建个体
            individual = {
                "tour": np.array(tour, dtype=np.int64),
                "cur_cost": float('inf')  # 初始成本设为无穷大，后续会计算
            }
            
            population.append(individual)
        
        return population
    
    @staticmethod
    def greedy_init(distance_matrix, pop_size):
        """
        贪心算法初始化种群
        
        参数:
            distance_matrix (numpy.ndarray): 距离矩阵
            pop_size (int): 种群大小
            
        返回:
            list: 初始化的种群列表
        """
        n = distance_matrix.shape[0]
        logger.info(f"贪心初始化种群，城市数量: {n}, 种群大小: {pop_size}")
        population = []
        
        for _ in range(pop_size):
            # 随机选择起始城市
            start_city = random.randint(0, n - 1)
            tour = [start_city]
            unvisited = set(range(n))
            unvisited.remove(start_city)
            
            # 贪心选择最近的城市
            current_city = start_city
            while unvisited:
                # 找到距离当前城市最近的未访问城市
                next_city = min(unvisited, key=lambda city: distance_matrix[current_city, city])
                tour.append(next_city)
                unvisited.remove(next_city)
                current_city = next_city
            
            # 创建个体
            individual = {
                "tour": np.array(tour, dtype=np.int64),
                "cur_cost": float('inf')  # 初始成本设为无穷大，后续会计算
            }
            
            population.append(individual)
        
        return population
    
    @staticmethod
    def mixed_init(distance_matrix, pop_size, greedy_ratio=0.3):
        """
        混合初始化种群（部分贪心，部分随机）
        
        参数:
            distance_matrix (numpy.ndarray): 距离矩阵
            pop_size (int): 种群大小
            greedy_ratio (float): 贪心初始化的比例
            
        返回:
            list: 初始化的种群列表
        """
        n = distance_matrix.shape[0]
        logger.info(f"混合初始化种群，城市数量: {n}, 种群大小: {pop_size}, 贪心比例: {greedy_ratio}")
        
        # 计算贪心和随机的数量
        greedy_count = int(pop_size * greedy_ratio)
        random_count = pop_size - greedy_count
        
        # 贪心初始化
        greedy_population = INIT.greedy_init(distance_matrix, greedy_count)
        
        # 随机初始化
        random_population = INIT.random_init(n, random_count)
        
        # 合并种群
        population = greedy_population + random_population
        
        return population
    
    @staticmethod
    def calculate_population_costs(population, distance_matrix):
        """
        计算种群中每个个体的路径成本
        
        参数:
            population (list): 种群列表
            distance_matrix (numpy.ndarray): 距离矩阵
            
        返回:
            list: 更新了成本的种群列表
        """
        from gls_evol_enhanced import tour_cost
        
        for individual in population:
            individual["cur_cost"] = tour_cost(distance_matrix, individual["tour"])
        return population