"""
统计分析专家模块

包含StatsExpert类，负责种群的统计学特征分析。
"""

from expert_base import ExpertBase
import utils


class StatsExpert(ExpertBase):
    """统计分析专家，负责种群的统计学特征分析"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, populations, distance_matrix=None):
        """分析种群的统计学特征
        
        参数:
            populations: 种群列表
            distance_matrix: 距离矩阵（可选）
            
        返回:
            统计分析结果
        """
        self.logger.info("开始统计分析")
        
        if not populations:
            return {
                "population_size": 0,
                "cost_stats": {"min": 0, "max": 0, "mean": 0, "std": 0},
                "diversity_level": 0,
                "distance_matrix": distance_matrix
            }
        
        # 计算成本统计
        costs = [p["cur_cost"] for p in populations if "cur_cost" in p]
        if not costs:
            cost_stats = {"min": 0, "max": 0, "mean": 0, "std": 0}
        else:
            cost_stats = {
                "min": min(costs),
                "max": max(costs),
                "mean": sum(costs) / len(costs),
                "std": self._calculate_std(costs)
            }
        
        # 计算种群多样性
        diversity = utils.calculate_population_diversity(populations)
        
        analysis_result = {
            "population_size": len(populations),
            "cost_stats": cost_stats,
            "diversity_level": diversity,
            "distance_matrix": distance_matrix
        }
        
        self.logger.info(f"统计分析完成: 种群大小={len(populations)}, 最优成本={cost_stats['min']}, 多样性={diversity:.3f}")
        return analysis_result
    
    def generate_report(self, analysis_result, coordinates=None, distance_matrix=None):
        """生成统计分析报告
        
        参数:
            analysis_result: 分析结果
            coordinates: 坐标信息（可选）
            distance_matrix: 距离矩阵（可选）
            
        返回:
            统计分析报告
        """
        report = {
            "population_size": analysis_result["population_size"],
            "cost_stats": analysis_result["cost_stats"],
            "diversity_level": analysis_result["diversity_level"],
            "coordinates": coordinates,
            "distance_matrix": distance_matrix
        }
        
        # 添加额外的统计信息
        if analysis_result["cost_stats"]["max"] > 0:
            report["cost_range"] = analysis_result["cost_stats"]["max"] - analysis_result["cost_stats"]["min"]
            report["cost_variation"] = analysis_result["cost_stats"]["std"] / analysis_result["cost_stats"]["mean"] if analysis_result["cost_stats"]["mean"] > 0 else 0
        else:
            report["cost_range"] = 0
            report["cost_variation"] = 0
        
        return report
    
    def _calculate_std(self, values):
        """计算标准差
        
        参数:
            values: 数值列表
            
        返回:
            标准差
        """
        if len(values) <= 1:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return variance ** 0.5
