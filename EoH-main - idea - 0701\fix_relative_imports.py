#!/usr/bin/env python3
"""
修复所有相对导入问题的脚本
"""

import os
import re

def fix_relative_imports_in_file(file_path):
    """修复单个文件中的相对导入问题"""
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 获取相对路径
        relative_path = file_path.replace('\\', '/').replace('src/', '')
        
        # 添加sys.path设置的通用代码
        sys_path_code = """import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))"""
        
        # 检查是否已经有sys.path设置
        has_sys_path = 'sys.path.append' in content
        
        # 修复不同模块的导入
        if relative_path.startswith('experts/'):
            # 专家模块的导入修复
            if not has_sys_path and ('from ...utils' in content or 'from ...' in content):
                # 在import语句前添加sys.path设置
                import_match = re.search(r'^(import|from)', content, re.MULTILINE)
                if import_match:
                    insert_pos = import_match.start()
                    content = content[:insert_pos] + sys_path_code + '\n' + content[insert_pos:]
                    has_sys_path = True
            
            # 替换相对导入
            content = re.sub(r'from \.\.\.utils import', 'from utils import', content)
            content = re.sub(r'from \.\.\.utils\.', 'from utils.', content)
            content = re.sub(r'from \.\.core\.', 'from core.', content)
            
        elif relative_path.startswith('core/'):
            # 核心模块的导入修复
            if not has_sys_path and ('from ...utils' in content or 'from ...' in content):
                import_match = re.search(r'^(import|from)', content, re.MULTILINE)
                if import_match:
                    insert_pos = import_match.start()
                    content = content[:insert_pos] + sys_path_code + '\n' + content[insert_pos:]
                    has_sys_path = True
            
            # 替换相对导入
            content = re.sub(r'from \.\.\.utils import', 'from utils import', content)
            content = re.sub(r'from \.\.\.utils\.', 'from utils.', content)
            
        elif relative_path.startswith('api/'):
            # API模块的导入修复
            if not has_sys_path and ('from ..config' in content or 'from ..utils' in content):
                import_match = re.search(r'^(import|from)', content, re.MULTILINE)
                if import_match:
                    insert_pos = import_match.start()
                    content = content[:insert_pos] + sys_path_code + '\n' + content[insert_pos:]
                    has_sys_path = True
            
            content = re.sub(r'from \.\.config import', 'from config import', content)
            content = re.sub(r'from \.\.utils import', 'from utils import', content)
            
        elif relative_path.startswith('knowledge/'):
            # 知识库模块的导入修复
            if not has_sys_path and ('from ..utils' in content or 'from ..' in content):
                import_match = re.search(r'^(import|from)', content, re.MULTILINE)
                if import_match:
                    insert_pos = import_match.start()
                    content = content[:insert_pos] + sys_path_code + '\n' + content[insert_pos:]
                    has_sys_path = True
            
            content = re.sub(r'from \.\.utils import', 'from utils import', content)
            content = re.sub(r'from \.\.utils\.', 'from utils.', content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"修复相对导入: {file_path}")
            return True
        
        return False
        
    except Exception as e:
        print(f"修复文件失败 {file_path}: {e}")
        return False

def fix_all_relative_imports():
    """修复所有Python文件的相对导入问题"""
    
    src_directory = 'src'
    
    if not os.path.exists(src_directory):
        print(f"源代码目录 {src_directory} 不存在")
        return
    
    fixed_files = 0
    total_files = 0
    
    for root, dirs, files in os.walk(src_directory):
        for file in files:
            if file.endswith('.py') and file != '__init__.py':
                file_path = os.path.join(root, file)
                total_files += 1
                if fix_relative_imports_in_file(file_path):
                    fixed_files += 1
    
    print(f"相对导入修复完成: 修复了 {fixed_files}/{total_files} 个文件")

if __name__ == "__main__":
    print("开始修复所有相对导入问题...")
    fix_all_relative_imports()
    print("修复完成！")
