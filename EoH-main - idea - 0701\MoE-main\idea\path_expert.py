"""
路径结构专家模块

包含PathExpert类，负责分析路径结构特征。
"""

from expert_base import ExpertBase
from collections import Counter, defaultdict


class PathExpert(ExpertBase):
    """路径结构专家，分析路径结构特征"""
    
    def __init__(self):
        super().__init__()
    
    def analyze(self, populations, distance_matrix):
        """分析路径结构特征
        
        参数:
            populations: 种群列表
            distance_matrix: 距离矩阵
            
        返回:
            路径结构分析结果
        """
        self.logger.info("开始路径结构分析")
        
        if not populations:
            return {
                "common_edges": [],
                "path_patterns": {},
                "structural_features": {},
                "edge_frequency": {},
                "path_similarity": 0.0
            }
        
        # 分析公共边
        common_edges = self._analyze_common_edges(populations)
        
        # 分析路径模式
        path_patterns = self._analyze_path_patterns(populations)
        
        # 分析结构特征
        structural_features = self._analyze_structural_features(populations, distance_matrix)
        
        # 计算边频率
        edge_frequency = self._calculate_edge_frequency(populations)
        
        # 计算路径相似性
        path_similarity = self._calculate_path_similarity(populations)
        
        analysis_result = {
            "common_edges": common_edges,
            "path_patterns": path_patterns,
            "structural_features": structural_features,
            "edge_frequency": edge_frequency,
            "path_similarity": path_similarity
        }
        
        self.logger.info(f"路径结构分析完成: 公共边数量={len(common_edges)}, 路径相似性={path_similarity:.3f}")
        return analysis_result
    
    def generate_report(self, analysis_result):
        """生成路径分析报告
        
        参数:
            analysis_result: 分析结果
            
        返回:
            路径分析报告
        """
        return {
            "common_edges": analysis_result["common_edges"],
            "path_patterns": analysis_result["path_patterns"],
            "structural_features": analysis_result["structural_features"],
            "edge_frequency": analysis_result["edge_frequency"],
            "path_similarity": analysis_result["path_similarity"],
            "analysis_summary": {
                "high_frequency_edges": len([e for e, freq in analysis_result["edge_frequency"].items() if freq > 0.5]),
                "unique_patterns": len(analysis_result["path_patterns"]),
                "structural_diversity": 1.0 - analysis_result["path_similarity"]
            }
        }
    
    def _analyze_common_edges(self, populations):
        """分析种群中的公共边
        
        参数:
            populations: 种群列表
            
        返回:
            公共边列表
        """
        if not populations:
            return []
        
        # 收集所有边
        all_edges = []
        for individual in populations:
            if "tour" in individual:
                tour = individual["tour"]
                edges = [(tour[i], tour[(i + 1) % len(tour)]) for i in range(len(tour))]
                # 标准化边（较小的节点在前）
                edges = [tuple(sorted(edge)) for edge in edges]
                all_edges.extend(edges)
        
        # 统计边频率
        edge_counts = Counter(all_edges)
        total_individuals = len(populations)
        
        # 找出出现频率超过50%的边
        common_edges = [edge for edge, count in edge_counts.items() if count / total_individuals > 0.5]
        
        return common_edges
    
    def _analyze_path_patterns(self, populations):
        """分析路径模式
        
        参数:
            populations: 种群列表
            
        返回:
            路径模式字典
        """
        patterns = defaultdict(int)
        
        for individual in populations:
            if "tour" in individual:
                tour = individual["tour"]
                # 分析3-opt模式（连续3个城市的模式）
                for i in range(len(tour)):
                    pattern = tuple(sorted([tour[i], tour[(i + 1) % len(tour)], tour[(i + 2) % len(tour)]]))
                    patterns[pattern] += 1
        
        # 转换为普通字典并计算频率
        pattern_freq = {}
        total_patterns = sum(patterns.values())
        for pattern, count in patterns.items():
            if total_patterns > 0:
                pattern_freq[str(pattern)] = count / total_patterns
        
        return pattern_freq
    
    def _analyze_structural_features(self, populations, distance_matrix):
        """分析结构特征
        
        参数:
            populations: 种群列表
            distance_matrix: 距离矩阵
            
        返回:
            结构特征字典
        """
        if not populations or distance_matrix is None:
            return {}
        
        features = {
            "avg_edge_length": 0.0,
            "edge_length_variance": 0.0,
            "tour_compactness": 0.0
        }
        
        all_edge_lengths = []
        
        for individual in populations:
            if "tour" in individual:
                tour = individual["tour"]
                edge_lengths = []
                
                for i in range(len(tour)):
                    from_city = tour[i]
                    to_city = tour[(i + 1) % len(tour)]
                    if from_city < len(distance_matrix) and to_city < len(distance_matrix[0]):
                        edge_length = distance_matrix[from_city][to_city]
                        edge_lengths.append(edge_length)
                
                all_edge_lengths.extend(edge_lengths)
        
        if all_edge_lengths:
            features["avg_edge_length"] = sum(all_edge_lengths) / len(all_edge_lengths)
            
            # 计算方差
            mean_length = features["avg_edge_length"]
            variance = sum((length - mean_length) ** 2 for length in all_edge_lengths) / len(all_edge_lengths)
            features["edge_length_variance"] = variance
            
            # 计算紧凑性（较小的方差表示更紧凑的路径）
            features["tour_compactness"] = 1.0 / (1.0 + variance) if variance > 0 else 1.0
        
        return features
    
    def _calculate_edge_frequency(self, populations):
        """计算边频率
        
        参数:
            populations: 种群列表
            
        返回:
            边频率字典
        """
        if not populations:
            return {}
        
        edge_counts = Counter()
        total_individuals = len(populations)
        
        for individual in populations:
            if "tour" in individual:
                tour = individual["tour"]
                edges = [(tour[i], tour[(i + 1) % len(tour)]) for i in range(len(tour))]
                # 标准化边
                edges = [tuple(sorted(edge)) for edge in edges]
                edge_counts.update(edges)
        
        # 计算频率
        edge_frequency = {}
        for edge, count in edge_counts.items():
            edge_frequency[str(edge)] = count / total_individuals
        
        return edge_frequency
    
    def _calculate_path_similarity(self, populations):
        """计算路径相似性
        
        参数:
            populations: 种群列表
            
        返回:
            平均路径相似性
        """
        if len(populations) < 2:
            return 0.0
        
        similarities = []
        
        for i in range(len(populations)):
            for j in range(i + 1, len(populations)):
                if "tour" in populations[i] and "tour" in populations[j]:
                    tour1 = populations[i]["tour"]
                    tour2 = populations[j]["tour"]
                    
                    # 计算汉明距离相似性
                    if len(tour1) == len(tour2):
                        same_positions = sum(1 for a, b in zip(tour1, tour2) if a == b)
                        similarity = same_positions / len(tour1)
                        similarities.append(similarity)
        
        return sum(similarities) / len(similarities) if similarities else 0.0
