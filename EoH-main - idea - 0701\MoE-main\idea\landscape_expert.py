"""
景观分析专家模块

包含LandscapeExpert类，负责整合其他专家的分析结果，生成综合景观分析。
"""

from expert_base import ExpertBase
from experts_prompt import LANDSCAPE_PROMPT, generate_landscape_expert_prompt, parse_expert_response


class LandscapeExpert(ExpertBase):
    """景观分析专家，整合其他专家的分析结果"""
    
    def __init__(self, interface_llm):
        super().__init__()
        self.interface_llm = interface_llm
        # Use the optimized landscape prompt template from experts_prompt.py
        self.prompt_template = LANDSCAPE_PROMPT
    
    def analyze(self, stats_report, path_report, elite_report, iteration=0, total_iterations=10, history_data=None):
        """整合分析结果，生成综合景观分析"""
        self.logger.info("开始景观分析")
        
        # Generate the optimized prompt
        prompt = generate_landscape_expert_prompt(
            stats_report=stats_report, 
            path_report=path_report, 
            elite_report=elite_report,
            iteration=iteration,
            total_iterations=total_iterations,
            history_data=history_data
        )
        
        # 调用LLM获取分析结果
        self.logger.info("调用LLM进行景观分析")
        self.logger.info(f"发送给LLM的提示词: {prompt}")
        landscape_analysis_text = self.interface_llm.get_response(prompt)
        self.logger.info(f"LLM返回的分析结果: {landscape_analysis_text}")
        
        # 解析LLM返回的文本为JSON对象
        landscape_analysis = parse_expert_response(landscape_analysis_text)
        
        # 如果解析失败，创建一个基本的结构化数据
        if "error" in landscape_analysis:
            self.logger.warning(f"解析景观分析结果失败: {landscape_analysis['error']}")
            # 创建一个基本结构，包含原始文本
            landscape_analysis = {
                "search_space_features": {"ruggedness": 0.5, "modality": "unknown", "deceptiveness": "unknown"},
                "population_state": {"diversity": 0.5, "convergence": 0.5, "clustering": "unknown"},
                "difficult_regions": [],
                "opportunity_regions": [],
                "evolution_phase": "unknown",
                "evolution_direction": {"recommended_focus": "balance", "operators": []},
                "raw_text": landscape_analysis_text  # 保留原始文本以备后用
            }
        
        self.logger.info("=====景观分析完成====")
        return landscape_analysis
    
    def generate_report(self, analysis_result):
        """直接返回LLM生成的分析结果"""
        return analysis_result
