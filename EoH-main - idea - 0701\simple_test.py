#!/usr/bin/env python3
"""
简单测试脚本 - 验证基本功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

print("开始测试项目重构后的功能...")

# 测试1: 配置模块
print("\n1. 测试配置模块...")
try:
    from config.config import ALGORITHM_CONFIG
    print("✓ 配置模块导入成功")
    print(f"  算法配置键: {list(ALGORITHM_CONFIG.keys())}")
except Exception as e:
    print(f"✗ 配置模块导入失败: {e}")

# 测试2: 专家基础模块
print("\n2. 测试专家基础模块...")
try:
    from experts.base.expert_base import ExpertBase
    print("✓ 专家基础模块导入成功")
except Exception as e:
    print(f"✗ 专家基础模块导入失败: {e}")

# 测试3: 核心算法模块
print("\n3. 测试核心算法模块...")
try:
    from core.algorithms.gls_evol_enhanced import tour_cost
    print("✓ 核心算法模块导入成功")
except Exception as e:
    print(f"✗ 核心算法模块导入失败: {e}")

# 测试4: 数据模块
print("\n4. 测试数据模块...")
try:
    from core.data.loadinstance import load_all_instances
    print("✓ 数据模块导入成功")
except Exception as e:
    print(f"✗ 数据模块导入失败: {e}")

# 测试5: 检查目录结构
print("\n5. 检查目录结构...")
expected_dirs = [
    'src/core/algorithms',
    'src/experts/base',
    'src/experts/management',
    'src/config',
    'data/benchmark/MMTSP',
    'src/tools/analysis'
]

for dir_path in expected_dirs:
    if os.path.exists(dir_path):
        print(f"✓ 目录存在: {dir_path}")
    else:
        print(f"✗ 目录缺失: {dir_path}")

print("\n测试完成!")
print("=" * 50)
print("项目重构验证结果:")
print("- 新的目录结构已创建")
print("- 文件已按功能分类迁移")
print("- 基本导入功能正常")
print("=" * 50)
