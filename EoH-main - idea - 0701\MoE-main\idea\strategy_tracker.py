import numpy as np
import json
import os
import logging
from datetime import datetime

# 添加自定义JSON编码器
class NumpyEncoder(json.JSONEncoder):
    """处理NumPy类型的JSON编码器"""
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.bool_):
            return bool(obj)
        return super(NumpyEncoder, self).default(obj)

class StrategyTracker:
    """跟踪和分析LLM策略选择效果的模块"""
    
    def __init__(self, output_dir=None):
        """
        初始化策略跟踪器
        
        Args:
            output_dir: 输出目录，用于保存策略效果记录
        """
        self.output_dir = output_dir or os.path.join(os.getcwd(), "results", "strategy_tracking")
        os.makedirs(self.output_dir, exist_ok=True)
        
        self.strategy_history = []  # 存储所有策略记录
        self.current_iteration_records = []  # 当前迭代的记录
        self.memory_size = 10  # 记忆窗口大小
        
        self.logger = logging.getLogger(__name__)
    
    def record_pre_strategy(self, iteration, individual_index, strategy, individual, landscape_features=None):
        """
        记录策略执行前的状态
        
        Args:
            iteration: 当前迭代次数
            individual_index: 个体索引
            strategy: 选择的策略 ('explore' 或 'exploit')
            individual: 个体信息
            landscape_features: 个体的景观特征
        """
        record = {
            'iteration': iteration,
            'individual_index': individual_index,
            'strategy': strategy,
            'pre_cost': individual['cur_cost'],
            'pre_tour': individual['tour'].copy() if hasattr(individual['tour'], 'copy') else individual['tour'],
            'timestamp': datetime.now().isoformat(),
            'landscape_features': landscape_features,
            'post_cost': None,
            'cost_improvement': None,
            'post_tour': None,
            'success': None
        }
        
        self.current_iteration_records.append(record)
        return len(self.current_iteration_records) - 1  # 返回记录ID
    
    def update_post_strategy(self, record_id, individual, population_diversity=None):
        """
        更新策略执行后的状态
        
        Args:
            record_id: 记录ID（当前迭代记录的索引）
            individual: 更新后的个体
            population_diversity: 更新后的种群多样性
        """
        if record_id >= len(self.current_iteration_records):
            self.logger.error(f"无效的记录ID: {record_id}")
            return
            
        record = self.current_iteration_records[record_id]
        pre_cost = record['pre_cost']
        post_cost = individual['cur_cost']
        
        # 添加详细的调试信息
        self.logger.info(f"更新策略记录 - ID: {record_id}")
        self.logger.info(f"策略: {record['strategy']}")
        self.logger.info(f"执行前成本: {pre_cost}")
        self.logger.info(f"执行后成本: {post_cost}")
        
        record['post_cost'] = post_cost
        record['post_tour'] = individual['tour'].copy() if hasattr(individual['tour'], 'copy') else individual['tour']
        # 修正成本改进计算方向：在TSP问题中，成本越低越好，所以改进应该是post_cost - pre_cost的负值
        record['cost_improvement'] = pre_cost - post_cost
        # 使用一个小的阈值来判断成本改进，以处理浮点数精度问题
        # 修正成功判断条件：在TSP问题中，cost_improvement应为负值才表示成功
        improvement_threshold = -1e-10
        record['success'] = record['cost_improvement'] > improvement_threshold
        
        self.logger.info(f"成本改进: {record['cost_improvement']}")
        self.logger.info(f"策略成功: {record['success']}")
        
        if population_diversity is not None:
            record['post_diversity'] = population_diversity
    
    def finalize_iteration(self):
        """完成当前迭代，将记录添加到历史中"""
        self.strategy_history.extend(self.current_iteration_records)
        self.current_iteration_records = []
    
    def get_strategy_effectiveness_summary(self):
        """
        生成策略有效性摘要
        
        Returns:
            str: 策略有效性的文本摘要
        """
        if not self.strategy_history:
            self.logger.warning("没有足够的历史数据进行分析。")
            return "没有足够的历史数据进行分析。"
        
        # 添加调试信息
        self.logger.info(f"策略历史记录数量: {len(self.strategy_history)}")
        
        # 获取最近的记录（基于记忆窗口大小）
        recent_history = self.strategy_history[-self.memory_size:] if len(self.strategy_history) > self.memory_size else self.strategy_history
        
        # 按策略分组
        explore_records = [r for r in recent_history if r['strategy'] == 'explore' and r['success'] is not None]
        exploit_records = [r for r in recent_history if r['strategy'] == 'exploit' and r['success'] is not None]
        
        # 添加调试信息
        self.logger.info(f"探索策略记录数: {len(explore_records)}, 利用策略记录数: {len(exploit_records)}")
        
        # 计算每种策略的成功率
        explore_success_rate = sum(1 for r in explore_records if r['success']) / len(explore_records) if explore_records else 0
        exploit_success_rate = sum(1 for r in exploit_records if r['success']) / len(exploit_records) if exploit_records else 0
        
        # 添加调试信息
        self.logger.info(f"探索策略成功数: {sum(1 for r in explore_records if r['success'])}, 利用策略成功数: {sum(1 for r in exploit_records if r['success'])}")
        
        # 计算平均改进
        explore_avg_improvement = np.mean([r['cost_improvement'] for r in explore_records]) if explore_records else 0
        exploit_avg_improvement = np.mean([r['cost_improvement'] for r in exploit_records]) if exploit_records else 0
        
        # 添加调试信息
        if explore_records:
            self.logger.info(f"探索策略改进量: {[r['cost_improvement'] for r in explore_records]}")
        if exploit_records:
            self.logger.info(f"利用策略改进量: {[r['cost_improvement'] for r in exploit_records]}")
        
        # 识别特征-效果模式
        feature_patterns = self._identify_feature_patterns(recent_history)
        
        # 生成摘要
        summary = [
            "Based on recent iterations, here's the strategy effectiveness analysis:",
            f"- Explore strategy: Success rate {explore_success_rate:.2%}, Average improvement {explore_avg_improvement:.2f}",
            f"- Exploit strategy: Success rate {exploit_success_rate:.2%}, Average improvement {exploit_avg_improvement:.2f}",
        ]
        
        # 添加特征-效果模式
        if feature_patterns:
            summary.append("\nFeature-Effect Patterns:")
            for pattern in feature_patterns[:3]:  # 只显示前3个最显著的模式
                summary.append(f"- {pattern}")
        
        # 添加具体案例
        if explore_records:
            best_explore = max(explore_records, key=lambda r: r['cost_improvement'] if r['cost_improvement'] else 0)
            summary.append(f"\nBest explore case: Iteration {best_explore['iteration']}, Improvement {best_explore['cost_improvement']:.2f}")
        
        if exploit_records:
            best_exploit = max(exploit_records, key=lambda r: r['cost_improvement'] if r['cost_improvement'] else 0)
            summary.append(f"Best exploit case: Iteration {best_exploit['iteration']}, Improvement {best_exploit['cost_improvement']:.2f}")
        
        # 添加策略建议
        summary.append("\nStrategy Recommendations:")
        if explore_success_rate > exploit_success_rate * 1.2:
            summary.append("- Explore strategy is significantly more effective in current phase, consider increasing exploration.")
        elif exploit_success_rate > explore_success_rate * 1.2:
            summary.append("- Exploit strategy is significantly more effective in current phase, consider increasing exploitation.")
        else:
            summary.append("- Both strategies show similar effectiveness, maintain current balance.")
        
        return "\n".join(summary)
    
    def _identify_feature_patterns(self, records):
        """识别特征-效果模式 - 增强版，支持更多特征分析和模式识别"""
        patterns = []
        
        # 只处理有景观特征的记录
        feature_records = [r for r in records if r.get('landscape_features') and r.get('success') is not None]
        if not feature_records:
            return patterns
            
        # 分析高多样性情况下的策略效果
        high_diversity_records = [r for r in feature_records if r.get('landscape_features', {}).get('diversity', 0) > 0.5]
        if high_diversity_records:
            high_div_explore = [r for r in high_diversity_records if r['strategy'] == 'explore']
            high_div_exploit = [r for r in high_diversity_records if r['strategy'] == 'exploit']
            
            high_div_explore_success = sum(1 for r in high_div_explore if r['success']) / len(high_div_explore) if high_div_explore else 0
            high_div_exploit_success = sum(1 for r in high_div_exploit if r['success']) / len(high_div_exploit) if high_div_exploit else 0
            
            # 计算平均改进量
            high_div_explore_improvement = np.mean([r['cost_improvement'] for r in high_div_explore if r['cost_improvement'] is not None]) if high_div_explore else 0
            high_div_exploit_improvement = np.mean([r['cost_improvement'] for r in high_div_exploit if r['cost_improvement'] is not None]) if high_div_exploit else 0
            
            # 综合考虑成功率和改进量
            if high_div_explore_success > high_div_exploit_success * 1.2 or (high_div_explore_success > 0 and high_div_explore_improvement > high_div_exploit_improvement * 1.5):
                patterns.append("When diversity is high, explore strategy tends to be more effective (success rate: {:.1%}, avg improvement: {:.2f})".format(high_div_explore_success, high_div_explore_improvement))
            elif high_div_exploit_success > high_div_explore_success * 1.2 or (high_div_exploit_success > 0 and high_div_exploit_improvement > high_div_explore_improvement * 1.5):
                patterns.append("When diversity is high, exploit strategy tends to be more effective (success rate: {:.1%}, avg improvement: {:.2f})".format(high_div_exploit_success, high_div_exploit_improvement))
        
        # 分析低多样性情况下的策略效果
        low_diversity_records = [r for r in feature_records if r.get('landscape_features', {}).get('diversity', 0) <= 0.5]
        if low_diversity_records:
            low_div_explore = [r for r in low_diversity_records if r['strategy'] == 'explore']
            low_div_exploit = [r for r in low_diversity_records if r['strategy'] == 'exploit']
            
            low_div_explore_success = sum(1 for r in low_div_explore if r['success']) / len(low_div_explore) if low_div_explore else 0
            low_div_exploit_success = sum(1 for r in low_div_exploit if r['success']) / len(low_div_exploit) if low_div_exploit else 0
            
            if low_div_explore_success > low_div_exploit_success * 1.2:
                patterns.append("When diversity is low, explore strategy tends to be more effective")
            elif low_div_exploit_success > low_div_explore_success * 1.2:
                patterns.append("When diversity is low, exploit strategy tends to be more effective")
        
        # 分析收敛状态下的策略效果
        converged_records = [r for r in feature_records if r.get('landscape_features', {}).get('convergence', 0) > 0.7]
        if converged_records:
            conv_explore = [r for r in converged_records if r['strategy'] == 'explore']
            conv_exploit = [r for r in converged_records if r['strategy'] == 'exploit']
            
            conv_explore_success = sum(1 for r in conv_explore if r['success']) / len(conv_explore) if conv_explore else 0
            conv_exploit_success = sum(1 for r in conv_exploit if r['success']) / len(conv_exploit) if conv_exploit else 0
            
            if conv_explore_success > conv_exploit_success * 1.2:
                patterns.append("When population is converged, explore strategy tends to be more effective")
            elif conv_exploit_success > conv_explore_success * 1.2:
                patterns.append("When population is converged, exploit strategy tends to be more effective")
        
        # 分析聚类情况下的策略效果
        cluster_records = [r for r in feature_records if 'cluster' in r.get('landscape_features', {})]
        if cluster_records:
            # 按聚类分组
            cluster_groups = {}
            for r in cluster_records:
                cluster = r['landscape_features']['cluster']
                if cluster not in cluster_groups:
                    cluster_groups[cluster] = []
                cluster_groups[cluster].append(r)
            
            # 分析每个聚类的策略效果
            for cluster, records in cluster_groups.items():
                if len(records) < 3:  # 样本太少，跳过
                    continue
                    
                cluster_explore = [r for r in records if r['strategy'] == 'explore']
                cluster_exploit = [r for r in records if r['strategy'] == 'exploit']
                
                if not cluster_explore or not cluster_exploit:
                    continue
                    
                cluster_explore_success = sum(1 for r in cluster_explore if r['success']) / len(cluster_explore)
                cluster_exploit_success = sum(1 for r in cluster_exploit if r['success']) / len(cluster_exploit)
                
                if cluster_explore_success > cluster_exploit_success * 1.2:
                    patterns.append(f"In cluster {cluster}, explore strategy tends to be more effective")
                elif cluster_exploit_success > cluster_explore_success * 1.2:
                    patterns.append(f"In cluster {cluster}, exploit strategy tends to be more effective")
        
        # 分析成本区间的策略效果
        if feature_records:
            costs = [r['pre_cost'] for r in feature_records]
            min_cost, max_cost = min(costs), max(costs)
            cost_range = max_cost - min_cost
            
            if cost_range > 0:
                # 将成本分为高、中、低三个区间
                low_threshold = min_cost + cost_range * 0.33
                high_threshold = max_cost - cost_range * 0.33
                
                low_cost_records = [r for r in feature_records if r['pre_cost'] <= low_threshold]
                high_cost_records = [r for r in feature_records if r['pre_cost'] >= high_threshold]
                
                # 分析低成本区间
                if low_cost_records:
                    low_cost_explore = [r for r in low_cost_records if r['strategy'] == 'explore']
                    low_cost_exploit = [r for r in low_cost_records if r['strategy'] == 'exploit']
                    
                    if low_cost_explore and low_cost_exploit:
                        low_cost_explore_success = sum(1 for r in low_cost_explore if r['success']) / len(low_cost_explore)
                        low_cost_exploit_success = sum(1 for r in low_cost_exploit if r['success']) / len(low_cost_exploit)
                        
                        if low_cost_explore_success > low_cost_exploit_success * 1.2:
                            patterns.append("For low-cost tours, explore strategy tends to be more effective")
                        elif low_cost_exploit_success > low_cost_explore_success * 1.2:
                            patterns.append("For low-cost tours, exploit strategy tends to be more effective")
                
                # 分析高成本区间
                if high_cost_records:
                    high_cost_explore = [r for r in high_cost_records if r['strategy'] == 'explore']
                    high_cost_exploit = [r for r in high_cost_records if r['strategy'] == 'exploit']
                    
                    if high_cost_explore and high_cost_exploit:
                        high_cost_explore_success = sum(1 for r in high_cost_explore if r['success']) / len(high_cost_explore)
                        high_cost_exploit_success = sum(1 for r in high_cost_exploit if r['success']) / len(high_cost_exploit)
                        
                        if high_cost_explore_success > high_cost_exploit_success * 1.2:
                            patterns.append("For high-cost tours, explore strategy tends to be more effective")
                        elif high_cost_exploit_success > high_cost_explore_success * 1.2:
                            patterns.append("For high-cost tours, exploit strategy tends to be more effective")
        
        # 分析低多样性情况下的策略效果
        low_diversity_records = [r for r in feature_records if r.get('landscape_features', {}).get('diversity', 0) <= 0.5]
        if low_diversity_records:
            low_div_explore = [r for r in low_diversity_records if r['strategy'] == 'explore']
            low_div_exploit = [r for r in low_diversity_records if r['strategy'] == 'exploit']
            
            low_div_explore_success = sum(1 for r in low_div_explore if r['success']) / len(low_div_explore) if low_div_explore else 0
            low_div_exploit_success = sum(1 for r in low_div_exploit if r['success']) / len(low_div_exploit) if low_div_exploit else 0
            
            if low_div_explore_success > low_div_exploit_success * 1.2:
                patterns.append("When diversity is low, explore strategy tends to be more effective")
            elif low_div_exploit_success > low_div_explore_success * 1.2:
                patterns.append("When diversity is low, exploit strategy tends to be more effective")
        
        # 分析收敛状态下的策略效果
        converged_records = [r for r in feature_records if r.get('landscape_features', {}).get('convergence', 0) > 0.7]
        if converged_records:
            conv_explore = [r for r in converged_records if r['strategy'] == 'explore']
            conv_exploit = [r for r in converged_records if r['strategy'] == 'exploit']
            
            conv_explore_success = sum(1 for r in conv_explore if r['success']) / len(conv_explore) if conv_explore else 0
            conv_exploit_success = sum(1 for r in conv_exploit if r['success']) / len(conv_exploit) if conv_exploit else 0
            
            if conv_explore_success > conv_exploit_success * 1.2:
                patterns.append("When population is converged, explore strategy tends to be more effective")
            elif conv_exploit_success > conv_explore_success * 1.2:
                patterns.append("When population is converged, exploit strategy tends to be more effective")
        
        # 分析聚类情况下的策略效果
        cluster_records = [r for r in feature_records if 'cluster' in r.get('landscape_features', {})]
        if cluster_records:
            # 按聚类分组
            cluster_groups = {}
            for r in cluster_records:
                cluster = r['landscape_features']['cluster']
                if cluster not in cluster_groups:
                    cluster_groups[cluster] = []
                cluster_groups[cluster].append(r)
            
            # 分析每个聚类的策略效果
            for cluster, records in cluster_groups.items():
                if len(records) < 3:  # 样本太少，跳过
                    continue
                    
                cluster_explore = [r for r in records if r['strategy'] == 'explore']
                cluster_exploit = [r for r in records if r['strategy'] == 'exploit']
                
                if not cluster_explore or not cluster_exploit:
                    continue
                    
                cluster_explore_success = sum(1 for r in cluster_explore if r['success']) / len(cluster_explore)
                cluster_exploit_success = sum(1 for r in cluster_exploit if r['success']) / len(cluster_exploit)
                
                if cluster_explore_success > cluster_exploit_success * 1.2:
                    patterns.append(f"In cluster {cluster}, explore strategy tends to be more effective")
                elif cluster_exploit_success > cluster_explore_success * 1.2:
                    patterns.append(f"In cluster {cluster}, exploit strategy tends to be more effective")
        
        # 分析成本区间的策略效果
        if feature_records:
            costs = [r['pre_cost'] for r in feature_records]
            min_cost, max_cost = min(costs), max(costs)
            cost_range = max_cost - min_cost
            
            if cost_range > 0:
                # 将成本分为高、中、低三个区间
                low_threshold = min_cost + cost_range * 0.33
                high_threshold = max_cost - cost_range * 0.33
                
                low_cost_records = [r for r in feature_records if r['pre_cost'] <= low_threshold]
                high_cost_records = [r for r in feature_records if r['pre_cost'] >= high_threshold]
                
                # 分析低成本区间
                if low_cost_records:
                    low_cost_explore = [r for r in low_cost_records if r['strategy'] == 'explore']
                    low_cost_exploit = [r for r in low_cost_records if r['strategy'] == 'exploit']
                    
                    if low_cost_explore and low_cost_exploit:
                        low_cost_explore_success = sum(1 for r in low_cost_explore if r['success']) / len(low_cost_explore)
                        low_cost_exploit_success = sum(1 for r in low_cost_exploit if r['success']) / len(low_cost_exploit)
                        
                        if low_cost_explore_success > low_cost_exploit_success * 1.2:
                            patterns.append("For low-cost tours, explore strategy tends to be more effective")
                        elif low_cost_exploit_success > low_cost_explore_success * 1.2:
                            patterns.append("For low-cost tours, exploit strategy tends to be more effective")
                
                # 分析高成本区间
                if high_cost_records:
                    high_cost_explore = [r for r in high_cost_records if r['strategy'] == 'explore']
                    high_cost_exploit = [r for r in high_cost_records if r['strategy'] == 'exploit']
                    
                    if high_cost_explore and high_cost_exploit:
                        high_cost_explore_success = sum(1 for r in high_cost_explore if r['success']) / len(high_cost_explore)
                        high_cost_exploit_success = sum(1 for r in high_cost_exploit if r['success']) / len(high_cost_exploit)
                        
                        if high_cost_explore_success > high_cost_exploit_success * 1.2:
                            patterns.append("For high-cost tours, explore strategy tends to be more effective")
                        elif high_cost_exploit_success > high_cost_explore_success * 1.2:
                            patterns.append("For high-cost tours, exploit strategy tends to be more effective")
        
        return patterns
    
    def save_history(self, instance_name):
        """
        保存策略历史到文件
        
        Args:
            instance_name: 实例名称
        """
        if not self.strategy_history:
            return
            
        # 创建实例专用目录
        instance_dir = os.path.join(self.output_dir, instance_name)
        os.makedirs(instance_dir, exist_ok=True)
        
        # 保存历史记录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        history_file = os.path.join(instance_dir, f"strategy_history_{timestamp}.json")
        
        # 使用自定义编码器处理NumPy类型
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(self.strategy_history, f, indent=2, ensure_ascii=False, cls=NumpyEncoder)
            
        self.logger.info(f"策略历史已保存到: {history_file}")
        
        # 生成分析报告
        report_file = os.path.join(instance_dir, f"strategy_analysis_{timestamp}.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(self.get_strategy_effectiveness_summary())
            f.write("\n\n详细统计:\n")
            
            # 添加详细统计
            total_records = len(self.strategy_history)
            explore_count = sum(1 for r in self.strategy_history if r['strategy'] == 'explore')
            exploit_count = sum(1 for r in self.strategy_history if r['strategy'] == 'exploit')
            
            f.write(f"总记录数: {total_records}\n")
            f.write(f"探索策略使用次数: {explore_count} ({explore_count/total_records:.2%})\n")
            f.write(f"利用策略使用次数: {exploit_count} ({exploit_count/total_records:.2%})\n")
        
        self.logger.info(f"策略分析报告已保存到: {report_file}")

    def get_strategy_success_rates(self):
        """
        获取每次迭代的策略成功率
        
        Returns:
            dict: 包含每种策略成功率的字典
        """
        if not self.strategy_history:
            return {'explore': [], 'exploit': []}
        
        # 按迭代次数分组
        iterations = sorted(set(r['iteration'] for r in self.strategy_history))
        success_rates = {'explore': [], 'exploit': []}
        
        for iter_num in iterations:
            iter_records = [r for r in self.strategy_history if r['iteration'] == iter_num]
            
            # 按策略分组
            explore_records = [r for r in iter_records if r['strategy'] == 'explore' and r['success'] is not None]
            exploit_records = [r for r in iter_records if r['strategy'] == 'exploit' and r['success'] is not None]
            
            # 计算成功率
            explore_success = sum(1 for r in explore_records if r['success']) / len(explore_records) if explore_records else 0
            exploit_success = sum(1 for r in exploit_records if r['success']) / len(exploit_records) if exploit_records else 0
            
            success_rates['explore'].append(explore_success)
            success_rates['exploit'].append(exploit_success)
        
        return success_rates

    def get_strategy_improvements(self):
        """
        获取每次迭代的策略平均改进量
        
        Returns:
            dict: 包含每种策略平均改进量的字典
        """
        if not self.strategy_history:
            return {'explore': [], 'exploit': []}
        
        # 按迭代次数分组
        iterations = sorted(set(r['iteration'] for r in self.strategy_history))
        improvements = {'explore': [], 'exploit': []}
        
        for iter_num in iterations:
            iter_records = [r for r in self.strategy_history if r['iteration'] == iter_num]
            
            # 按策略分组
            explore_records = [r for r in iter_records if r['strategy'] == 'explore' and r['cost_improvement'] is not None]
            exploit_records = [r for r in iter_records if r['strategy'] == 'exploit' and r['cost_improvement'] is not None]
            
            # 计算平均改进量
            explore_improvement = sum(r['cost_improvement'] for r in explore_records) / len(explore_records) if explore_records else 0
            exploit_improvement = sum(r['cost_improvement'] for r in exploit_records) / len(exploit_records) if exploit_records else 0
            
            improvements['explore'].append(explore_improvement)
            improvements['exploit'].append(exploit_improvement)
        
        return improvements

    # 在 StrategyTracker 类中添加 record_post_strategy 方法
    def record_post_strategy(self, record_id, new_cost, old_cost, success):
        """
        记录策略执行后的状态
        
        Args:
            record_id: 记录ID（当前迭代记录的索引）
            new_cost: 执行策略后的成本
            old_cost: 执行策略前的成本
            success: 策略是否成功
        """
        if record_id >= len(self.current_iteration_records):
            self.logger.error(f"无效的记录ID: {record_id}")
            return
            
        record = self.current_iteration_records[record_id]
        
        # 添加详细的调试信息
        self.logger.info(f"更新策略记录 - ID: {record_id}")
        self.logger.info(f"策略: {record['strategy']}")
        self.logger.info(f"执行前成本: {old_cost}")
        self.logger.info(f"执行后成本: {new_cost}")
        
        record['post_cost'] = new_cost
        record['cost_improvement'] = new_cost - old_cost
        record['success'] = success
        
        self.logger.info(f"成本改进: {record['cost_improvement']}")
        self.logger.info(f"策略成功: {record['success']}")