import numpy as np
from numba import jit

# 添加全局变量用于跟踪评估次数
# evaluation_counter = np.array([0], dtype=np.int64, order='C')
# evaluation_counter.flags.writeable = True  # 关键：显式设置为可写
# evaluation_counter=0
# @jit(nopython=True)
# def increment_counter(counter_array, value):
#     counter_array[0] += value  # 现在可以安全修改
    
@jit(nopython=True)
def two_opt(tour: np.ndarray, i: int, j: int) -> np.ndarray:
    if i == j:
        return tour
    
    # 确保索引在有效范围内
    if i < 0 or i >= len(tour) or j < 0 or j >= len(tour):
        raise IndexError("索引超出范围")

    # 交换逻辑
    a = tour[i, 0]
    b = tour[j, 0]
    tour[i, 0], tour[i, 1] = tour[i, 1], j
    tour[j, 0] = i
    tour[a, 1] = b
    tour[b, 1] = tour[b, 0]
    tour[b, 0] = a
    c = tour[b, 1]
    # 添加最大迭代次数限制，防止无限循环
    max_iterations = len(tour)
    iteration_count = 0
    
    while tour[c, 1] != j:
        # 检查迭代次数
        if iteration_count >= max_iterations:
            break  # 超过最大迭代次数，退出循环
            
        # 检查索引有效性
        if c < 0 or c >= len(tour):
            break  # 索引无效，退出循环
            
        d = tour[c, 0]
        # 检查d的有效性
        if d < 0 or d >= len(tour):
            break  # 索引无效，退出循环
            
        tour[c, 0], tour[c, 1] = tour[c, 1], d  # 交换
        c = d
        iteration_count += 1
    return tour

@jit(nopython=True)
def two_opt_cost(tour, D, i, j):
    # global evaluation_counter
    if i == j:
        return 0

    a = tour[i,0]
    b = tour[j,0]
    delta = D[a, b] \
            + D[i, j] \
            - D[a, i] \
            - D[b, j]
    
    # # 每次调用该函数时，评估次数加4
    # increment_counter(evaluation_counter, 4)
    # evaluation_counter += 4
    
    return delta

@jit(nopython=True)
def two_opt_a2a(tour, D, N, first_improvement=False):
    best_move = None
    best_delta = 0

    # 优化：限制搜索范围，只考虑前N个最近邻居
    tour_len = len(tour)
    max_neighbors = min(20, len(N[0])) if tour_len > 100 else len(N[0])
    
    idxs = range(0, tour_len - 1)
    for i in idxs:
        # 只考虑最近的几个邻居，减少计算量
        for j_idx in range(min(max_neighbors, len(N[i]))):
            j = N[i][j_idx]
            
            # 检查索引是否有效
            if j >= tour_len:
                continue
                
            # 检查是否是有效的交换
            if (tour[i, 0] == j or tour[i, 1] == j or 
                tour[j, 0] == i or tour[j, 1] == i):
                continue
                
            # 计算成本变化
            delta = two_opt_cost(tour, D, i, j)
            if delta < best_delta and not np.isclose(0, delta):
                best_delta = delta
                best_move = i, j
                if first_improvement:
                    break
        
        # 如果使用first_improvement策略且找到了改进，立即退出
        if first_improvement and best_move is not None:
            break

    if best_move is not None:
        return best_delta, two_opt(tour, *best_move)
    return 0, tour

@jit(nopython=True)
def relocate(tour, i, j):
    a = tour[i,0]
    b = tour[i,1]
    tour[a,1] = b
    tour[b,0] = a

    d = tour[j,1]
    tour[d,0] = i
    tour[i,0] = j
    tour[i,1] = d
    tour[j,1] = i

    return tour

@jit(nopython=True)
def relocate_cost(tour, D, i, j):
    # global evaluation_counter
    if i == j:
        return 0

    a = tour[i,0]
    b = i
    c = tour[i,1]

    d = j
    e = tour[j,1]

    delta = - D[a, b] \
            - D[b, c] \
            + D[a, c] \
            - D[d, e] \
            + D[d, b] \
            + D[b, e]
    # 每次调用该函数时，评估次数加6
    # increment_counter(evaluation_counter, 6)
    # evaluation_counter += 6 
    return delta

@jit(nopython=True)
def relocate_a2a(tour, D, N, first_improvement=False, set_delta=0):
    best_move = None
    best_delta = 0  # 将初始值设置为0

    # 优化：限制搜索范围，只考虑前N个最近邻居
    tour_len = len(tour)
    max_neighbors = min(20, len(N[0])) if tour_len > 100 else len(N[0])
    
    idxs = range(tour_len - 1)
    for i in idxs:
        # 只考虑最近的几个邻居，减少计算量
        for j_idx in range(min(max_neighbors, len(N[i]))):
            j = N[i][j_idx]
            
            if tour[j, 1] == i:  # 跳过无效的搬迁选择
                continue

            # 计算成本变化
            delta = relocate_cost(tour, D, i, j)
            if delta < best_delta and not np.isclose(0, delta):
                best_delta = delta
                best_move = (i, j)
                if first_improvement:
                    break  # 仅中断内层循环

        # 如果使用first_improvement策略且找到了改进，立即退出
        if first_improvement and best_move is not None:
            break  # 外层循环也中断，跳出

    if best_move is not None:
        return best_delta, relocate(tour, *best_move)  # 应用最佳移动
    return 0, tour  # 如果没有有效移动

# # 获取评估次数的函数
# def get_evaluation_count():
#     return evaluation_counter
# def reset_evaluation_count():
#     global evaluation_counter
#     evaluation_counter = 0
