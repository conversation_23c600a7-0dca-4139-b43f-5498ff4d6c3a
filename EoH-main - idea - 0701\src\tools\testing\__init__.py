"""
Testing tools and utilities for TSP analysis.

This module provides testing utilities for:
- Path normalization testing
- Refactoring validation
- Solution cost updates and validation
"""

# Import testing functions for easy access
try:
    from .test_normalize_path import test_normalize_path_uniqueness, compare_implementations
    from .test_refactoring import test_response_parser
    from .update_solution_costs import update_solution_costs
except ImportError as e:
    print(f"Warning: Could not import testing tools: {e}")
    pass
