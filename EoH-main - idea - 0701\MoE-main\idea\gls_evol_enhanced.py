import time
import numpy as np
from numba import jit
import gls_operators
import random
import time
import copy
# 导入新的拓扑感知扰动算法
# from topology_aware_perturbation import topology_aware_perturbation


def perturb(path):
    """扰动操作"""
    n = len(path)
    # 随机选择两个不同的索引进行交换
    i, j = random.sample(range(n), 2)
    path[i], path[j] = path[j], path[i]
    return path

def perturb_reorder(path, dis_m=None, iteration=0, max_iterations=5, strategy='topology_aware', known_solutions=None, end_time=None):
    """增强型扰动操作：根据不同策略有针对性地扰动路径
    
    参数:
        path: 当前路径
        dis_m: 距离矩阵，用于计算边的成本
        iteration: 当前迭代次数
        max_iterations: 最大迭代次数
        strategy: 扰动策略，可选值为'cost_based', 'adaptive', 'random', 'topology_aware', 'auto'(随机选择策略)
        known_solutions: 已知的最优解列表，用于拓扑感知扰动
        end_time: 结束时间，用于检查是否超时
    """
    n = len(path)
    if n <= 3:
        return path.copy()  # 路径太短，直接返回
    # strategy= 'topology_aware'
    # 如果没有指定策略或策略为'auto'，随机选择一种
    if strategy is None or strategy == 'auto':
        strategies = ['cost_based', 'adaptive', 'random', 'topology_aware']
        # 随着迭代进行，增加使用topology_aware策略的概率
        if iteration > max_iterations * 0.3:
            # 中后期更倾向于使用拓扑感知扰动
            # weights = [0.2, 0.1, 0.1, 0.4]
            weights = [0.4, 0.1, 0.1, 0.4]
        else:
            # 前期更倾向于使用多样化策略
            # weights = [0.3, 0.2, 0.1, 0.2]
            weights = [0.5, 0.3, 0.1, 0.1]
        strategy = random.choices(strategies, weights=weights, k=1)[0]
    
    # 复制路径，避免修改原始路径
    path_copy = path.copy()
    
    # 使用多维度progress计算
    from progress_calculator import calculate_progress
    progress = calculate_progress(
        current_iteration=iteration,
        max_iterations=max_iterations,
        current_cost=None,  # 这里没有当前成本信息
        problem_size=len(path),
        time_limit=None if end_time is None else (end_time - time.time()),
        end_time=end_time
    )
    
    # 使用优化版拓扑感知扰动
    if strategy == 'topology_aware':
        # 导入优化版的拓扑感知扰动函数
        from optimized_topology_aware_perturbation import topology_aware_perturbation
        # 使用已计算的多维度进度值
        return topology_aware_perturbation(path_copy, dis_m, known_solutions, progress, end_time)
    
    elif strategy == 'cost_based' and dis_m is not None:
        # 基于成本的扰动：优先扰动成本高的边
        from optimized_topology_aware_perturbation import adaptive_random_perturbation
        edge_costs = []
        for i in range(n):
            # 每处理一定数量的边检查一次是否超时
            if end_time is not None and i % 10 == 0 and time.time() >= end_time:
                # 如果超时，退化为简单随机扰动
                return adaptive_random_perturbation(path_copy, 0.5, end_time)
                
            edge_cost = dis_m[path[i], path[(i+1) % n]]
            edge_costs.append((i, edge_cost))
        
        # 按成本降序排序
        edge_costs.sort(key=lambda x: x[1], reverse=True)
        
        # 选择成本最高的几条边所在的区域进行扰动
        top_edges = edge_costs[:max(2, n//5)]  # 选择至少2条或前20%的高成本边
        selected_indices = [e[0] for e in random.sample(top_edges, min(2, len(top_edges)))]
        selected_indices.sort()
        
        # 如果选择的是相邻的边，扰动这一段区域
        if len(selected_indices) >= 2:
            if selected_indices[1] - selected_indices[0] <= n//2:
                start, end = selected_indices[0], selected_indices[1]
            else:
                # 如果两个索引相距太远，随机选择一个区域
                start = random.randint(0, n-3)
                end = min(start + random.randint(2, max(3, n//3)), n-1)
        else:
            # 默认情况，随机选择
            start = random.randint(0, n-3)
            end = min(start + random.randint(2, max(3, n//3)), n-1)
    
    elif strategy == 'adaptive':
        # 自适应扰动：根据多维度进度调整扰动强度
        # progress已经在前面使用多维度方法计算过
        
        if progress < 0.3:
            # 早期：大范围扰动
            segment_size = max(3, int(n * 0.3))
        elif progress < 0.7:
            # 中期：中等范围扰动
            segment_size = max(3, int(n * 0.2))
        else:
            # 后期：小范围扰动
            segment_size = max(2, int(n * 0.1))
        
        start = random.randint(0, n - segment_size)
        end = min(start + segment_size, n)
    

    
    else:  # strategy == 'random' 或默认情况
        # 随机扰动：随机选择一段路径并打乱
        start = random.randint(0, n-3)
        end = min(start + random.randint(2, max(3, n//3)), n-1)
    
    # 对选定区域进行打乱（适用于cost_based, adaptive和random策略）
    # 确保path_copy是列表类型
    if isinstance(path_copy, np.ndarray):
        path_copy = path_copy.tolist()
    sub_path = path_copy[start:end]
    random.shuffle(sub_path)
    path_copy[start:end] = sub_path
    
    return path_copy




def normalize_path(path):
    """改进的标准化路径函数，使用字典序比较确保唯一表示"""
    path = np.array(path, dtype=np.int64)
    
    if len(path) == 0:
        return path  # 如果路径为空，直接返回
    
    n = len(path)
    min_path = None
    min_representation = None
    
    # 检查所有旋转变体
    for i in range(n):
        # 生成旋转变体
        rotated = np.concatenate([path[i:], path[:i]])
        # 计算字典序表示
        representation = tuple(rotated)
        
        # 如果这是第一个变体或者比当前最小的更小，则更新
        if min_representation is None or representation < min_representation:
            min_representation = representation
            min_path = rotated
    
    # 检查所有反转后的旋转变体
    reversed_path = path[::-1]
    for i in range(n):
        # 生成反转后的旋转变体
        rotated = np.concatenate([reversed_path[i:], reversed_path[:i]])
        # 计算字典序表示
        representation = tuple(rotated)
        
        # 如果比当前最小的更小，则更新
        if min_representation is not None and representation < min_representation:
            min_representation = representation
            min_path = rotated
    
    return min_path



# 计算共享距离，即两个路径中相同的边的数量
# @jit(nopython=True)
def share_distance(path1, path2):
    """计算两条路径之间的共享边数量"""
    # 添加安全检查，确保输入路径有效
    if path1 is None or path2 is None:
        return 0
    
    if isinstance(path1, np.ndarray) and len(path1) == 0:
        return 0
    
    if isinstance(path2, np.ndarray) and len(path2) == 0:
        return 0
    
    if not isinstance(path1, np.ndarray) and not path1:
        return 0
    
    if not isinstance(path2, np.ndarray) and not path2:
        return 0
        
    city_num = len(path1)
    # 确保path2长度与path1相同
    if len(path2) != city_num:
        # 如果有日志模块，可以记录警告
        # logging.warning(f"路径长度不匹配: path1={city_num}, path2={len(path2)}")
        return 0
        
    count = 0
    for i in range(city_num):
        p11 = path1[i]
        p12 = path1[(i + 1) % city_num]
        
        for j in range(len(path2)):  # 使用实际长度而不是city_num
            p21 = path2[j]
            # 使用try-except防止索引错误
            try:
                p22 = path2[(j + 1) % len(path2)]  # 使用path2的实际长度
                
                # 检查边是否相同（考虑方向）
                if (p11 == p21 and p12 == p22) or (p11 == p22 and p12 == p21):
                    count += 1
                    break
            except IndexError:
                # 如果有日志模块，可以记录错误
                # logging.error(f"索引错误: j={j}, path2长度={len(path2)}")
                continue
                
    return count

def share_distance_o2a_safe(path, tours):
    """计算一条路径与多条路径之间的共享边，返回是否存在共享边以及共享边最多的路径索引"""
    # 添加安全检查
    if not path or not tours or len(tours) == 0:
        return False, -1
        
    max_share = -1
    max_index = -1
    
    for i, tour in enumerate(tours):
        # 确保tour是有效的
        if not tour:
            continue
            
        try:
            share_distance_count = share_distance(path, tour)
            
            if share_distance_count > max_share:
                max_share = share_distance_count
                max_index = i
        except Exception as e:
            # logging.error(f"计算共享边时出错: {str(e)}")
            continue
    
    return max_share > 0, max_index


# @jit(nopython=True)
def share_distance_o2a(path1, tours):
    same_flag = False
    share_distance_count = 0
    index = -1
    tour_count = len(tours)

    if tour_count == 0:
        return same_flag, index
    
    for index in range(tour_count):
        tour = tours[index]
        share_distance_count = share_distance(path1, tour)
        if share_distance_count == len(path1):
            same_flag = True
            break

    return same_flag, index if same_flag else -1


def calculate_path_similarity(path1, path2):
    """
    计算两条路径之间的相似度
    
    参数:
        path1, path2: 两个路径
    
    返回:
        float: 相似度，范围[0,1]，值越大表示越相似
    """
    # 添加安全检查
    if path1 is None or path2 is None or len(path1) == 0 or len(path2) == 0:
        return 0.0
    
    # 确保路径长度相同
    if len(path1) != len(path2):
        return 0.0
    
    # 计算共享边数量
    shared_edges = share_distance(path1, path2)
    
    # 计算相似度：共享边数量除以路径长度
    similarity = shared_edges / len(path1)
    
    return similarity


def is_tsplib_instance(func_name):
    """
    判断一个实例是否为TSPLIB实例
    
    参数:
        func_name: 实例名称
    
    返回:
        bool: 是否为TSPLIB实例
    """
    # TSPLIB实例名称列表
    tsplib_instances = ['eil51', 'berlin52', 'st70', 'pr76', 'kroA100', 'lin105']
    
    # 检查实例名称是否在TSPLIB实例列表中
    return func_name in tsplib_instances



@jit(nopython=True)
def tour2route(tour):
    """将一维路径转换为二维路由格式"""
    n = len(tour)
    route = np.zeros((n, 2), dtype=np.int64)
    for i in range(n):
        route[tour[i], 0] = tour[(i - 1) % n]
        route[tour[i], 1] = tour[(i + 1) % n]
    return route

@jit(nopython=True)
def route2tour(route):
    """将二维路由格式转换为一维路径"""
    n = len(route)
    tour = np.zeros(n, dtype=np.int64)
    
    # 找到起点（任意一个城市）
    start = 0
    current = start
    next_city = route[current, 1]
    
    # 构建路径
    for i in range(n):
        tour[i] = current
        current = next_city
        next_city = route[current, 1]
    
    return tour

@jit(nopython=True)
def tour_cost_2End(dis_m, tour2End):
    c=0
    s = 0
    e = tour2End[0,1]
    for i in range(tour2End.shape[0]):
        c += dis_m[s,e]
        s = e
        e = tour2End[s,1]
    return c

@jit(nopython=True)
def tour_cost(D, tour):
    """计算路径代价
    
    Args:
        D: 距离矩阵
        tour: 路径
        
    Returns:
        cost: 路径总成本
    """
    cost = 0
    for i in range(len(tour) - 1):
        cost += D[tour[i], tour[i + 1]]
    cost += D[tour[-1], tour[0]]
    return cost


# 原始版本，保留详细信息输出
# def is_valid_tsp_tour(tour, num_cities=None):
#     """验证给定的tour是否为合法的TSP路径
    
#     Args:
#         tour: 待验证的路径，一维数组或列表
#         num_cities: 城市总数，如果为None则从tour中推断
        
#     Returns:
#         valid: 布尔值，表示路径是否合法
#         info: 字符串，包含验证结果的详细信息
#     """
#     # 安全检查
#     if tour is None or len(tour) == 0:
#         return False, "路径为空"
    
#     # 转换为numpy数组以便处理
#     if not isinstance(tour, np.ndarray):
#         tour = np.array(tour)
    
#     # 如果未指定城市数量，则假设为tour的长度
#     if num_cities is None:
#         num_cities = len(tour)
    
#     # 检查1: 路径长度是否等于城市总数
#     if len(tour) != num_cities:
#         return False, f"路径长度({len(tour)})不等于城市总数({num_cities})"
    
#     # 检查2: 路径中的城市是否在有效范围内 (0 到 num_cities-1)
#     if np.min(tour) < 0 or np.max(tour) >= num_cities:
#         return False, f"路径中包含无效的城市索引，有效范围应为0-{num_cities-1}"
    
#     # 检查3: 每个城市是否只出现一次
#     unique_cities = np.unique(tour)
#     if len(unique_cities) != num_cities:
#         return False, "路径中存在重复城市或缺少某些城市"
    
#     # 检查4: 是否包含所有城市 (0 到 num_cities-1)
#     expected_cities = set(range(num_cities))
#     actual_cities = set(tour)
#     missing_cities = expected_cities - actual_cities
#     if missing_cities:
#         return False, f"路径中缺少城市: {missing_cities}"
    
#     return True, "路径是有效的TSP路径"

# # 优化版本，使用numba加速，只返回布尔值
# # 定义一个非Numba版本的函数，用于处理num_cities为None的情况
# def is_valid_tsp_tour(tour, num_cities=None):
#     """验证给定的tour是否为合法的TSP路径（非Numba版本，可处理None参数）
    
#     Args:
#         tour: 待验证的路径，一维numpy数组
#         num_cities: 城市总数，如果为None则从tour中推断
        
#     Returns:
#         valid: 布尔值，表示路径是否合法
#     """
#     # 安全检查
#     if len(tour) == 0:
#         return False
    
#     # 如果未指定城市数量，则假设为tour的长度
#     if num_cities is None:
#         num_cities = len(tour)
        
#     # 调用Numba加速版本
#     return is_valid_tsp_tour_fast(tour, num_cities)

@jit(nopython=True)
def is_valid_tsp_tour_fast(tour, num_cities):
    """验证给定的tour是否为合法的TSP路径（优化版本，使用numba加速）
    
    Args:
        tour: 待验证的路径，一维numpy数组
        num_cities: 城市总数，必须提供确定的值
        
    Returns:
        valid: 布尔值，表示路径是否合法
    """
    # 安全检查
    if len(tour) == 0:
        return False
    
    # 检查1: 路径长度是否等于城市总数
    if len(tour) != num_cities:
        return False
    
    # 检查2: 路径中的城市是否在有效范围内 (0 到 num_cities-1)
    if np.min(tour) < 0 or np.max(tour) >= num_cities:
        return False
    
    # 检查3: 每个城市是否只出现一次（使用计数方法代替set操作）
    visited = np.zeros(num_cities, dtype=np.int32)
    for city in tour:
        visited[city] += 1
    
    for count in visited:
        if count != 1:
            return False
    
    return True



def guided_local_search(D, N, init_tour, init_cost, end_time, ite_max, perturbation_moves, evo_individual, evo_populations, res_populations, first_improvement=False, similarity_threshold=1.0, is_tsplib=False, cost_ratio=0.05):
    """引导局部搜索算法，集成了拓扑感知扰动"""
    # 导入时间跟踪器
    from time_tracker import time_tracker
    
    # 记录开始时间，用于监控总执行时间
    gls_start_time = time.time()
    time_tracker.start_component_timer('guided_local_search_total')
    
    cur_route, cur_cost = init_tour, init_cost
    # search_progress = []
    
    print(f"开始执行引导局部搜索，时间限制：{end_time - gls_start_time:.2f}秒")
    
    # 记录各个阶段的时间
    phase_times = {
        'two_opt': 0.0,
        'relocate': 0.0,
        'perturbation': 0.0,
        'population_update': 0.0
    }
    
    improved = True
    iter=0
    while iter < int(ite_max) and time.time() < end_time:

        while improved and time.time() < end_time:

            improved = False
            
            # 记录two_opt开始时间
            two_opt_start = time.time()
            time_tracker.start_component_timer('two_opt')
            delta, new_tour = gls_operators.two_opt_a2a(cur_route, D, N, first_improvement)#2_opt局部搜索算子改进解若路径变短继续循环进行改进
            if delta < 0 or (delta==0 and not np.array_equal(new_tour, cur_route)):
                improved = True
                cur_cost = tour_cost_2End(D,new_tour)
                cur_route = new_tour
            # 累计two_opt时间
            elapsed_time = time.time() - two_opt_start
            phase_times['two_opt'] += elapsed_time
            time_tracker.end_component_timer('two_opt')
            
            # 记录relocate开始时间
            relocate_start = time.time()
            time_tracker.start_component_timer('relocate')
            delta, new_tour = gls_operators.relocate_a2a(cur_route, D, N, first_improvement)#重定位局部搜索算子改进解
            if delta < 0 or (delta==0 and not np.array_equal(new_tour, cur_route)):
                improved = True
                cur_cost = tour_cost_2End(D,new_tour)
                cur_route = new_tour
            # 累计relocate时间
            elapsed_time = time.time() - relocate_start
            phase_times['relocate'] += elapsed_time
            time_tracker.end_component_timer('relocate')
                
               
        best_cost=cur_cost

        # 记录种群更新开始时间
        pop_update_start = time.time()
        time_tracker.start_component_timer('population_update')
        tour=route2tour(cur_route)

        # 初始化路径相似度优化器（如果尚未初始化）
        if not hasattr(guided_local_search, 'path_optimizer'):
            try:
                from path_similarity_optimizer import PathSimilarityOptimizer
                guided_local_search.path_optimizer = PathSimilarityOptimizer(similarity_threshold=similarity_threshold)
                # 将现有精英解添加到路径优化器中
                for res in res_populations:
                    if "tour" in res:
                        guided_local_search.path_optimizer.add_path(res["tour"])
                print(f"路径相似度优化器已初始化，相似度阈值：{similarity_threshold}")
            except ImportError:
                print("警告：无法导入PathSimilarityOptimizer，将使用原始相似度计算方法")
                guided_local_search.path_optimizer = None

        tours=[indival["tour"] for indival in evo_populations]
        in_tours_flag,index=share_distance_o2a_safe(tour,tours)
        res_tours=[res_indival["tour"] for res_indival in res_populations]
        
        # 使用路径相似度优化器检查是否与精英解相似
        if guided_local_search.path_optimizer is not None:
            is_similar_to_res, similar_res_id, max_similarity = guided_local_search.path_optimizer.check_similarity(tour)
            # 打印相似度信息（调试用）
            # print(f"路径相似度：{max_similarity:.4f}，阈值：{similarity_threshold}，是否相似：{is_similar_to_res}")
        else:
            # 如果没有路径优化器，使用原始方法
            in_res_tours_flag, inedx_res = share_distance_o2a_safe(tour, res_tours)
            is_similar_to_res = in_res_tours_flag
        
        if res_populations!=[]:
            best_res_cost=min(res_populations,key=lambda x:x["cur_cost"])["cur_cost"]
        else:
            best_res_cost=best_cost
            
        if not in_tours_flag:      
            evo_individual["cur_cost"]=cur_cost
            evo_individual["tour"]=tour
            
            # 根据是否为TSPLIB实例使用不同的加入条件
            if is_tsplib:
                # 检查是否满足成本条件：当前成本小于等于(1+x)*最佳成本
                cost_condition = cur_cost <= best_res_cost * (1 + cost_ratio) if res_populations else True
                
                # 检查是否满足相似度条件：与所有精英解的相似度都小于阈值
                similarity_condition = not is_similar_to_res
                
                # 同时满足成本条件和相似度条件时加入精英解，或者成本更优时也加入
                if (cost_condition and similarity_condition) or (cur_cost < best_res_cost):
                    res_populations.append(copy.deepcopy(evo_individual))
                    # 将新加入的路径添加到路径优化器
                    if guided_local_search.path_optimizer is not None:
                        guided_local_search.path_optimizer.add_path(tour)
                elif evo_individual["cur_cost"] <= best_cost:
                    evo_populations.append(copy.deepcopy(evo_individual))
            else:
                # 使用原始逻辑，但替换相似度检查
                if cur_cost <= best_res_cost and not is_similar_to_res:
                    res_populations.append(copy.deepcopy(evo_individual))
                    # 将新加入的路径添加到路径优化器
                    if guided_local_search.path_optimizer is not None:
                        guided_local_search.path_optimizer.add_path(tour)
                elif evo_individual["cur_cost"] <= best_cost:
                    evo_populations.append(copy.deepcopy(evo_individual))
        else:  
            if evo_populations[index]["cur_cost"] <= best_cost:
                if not is_similar_to_res:
                    if evo_populations[index]["cur_cost"] <= best_res_cost:
                        res_populations.append(copy.deepcopy(evo_populations[index]))
                        # 将新加入的路径添加到路径优化器
                        if guided_local_search.path_optimizer is not None:
                            guided_local_search.path_optimizer.add_path(evo_populations[index]["tour"])
                else:
                    evo_populations.pop(index)
        # 累计种群更新时间
        elapsed_time = time.time() - pop_update_start
        phase_times['population_update'] += elapsed_time
        time_tracker.end_component_timer('population_update')


        best_path=route2tour(cur_route)
        best_cost=tour_cost(D,best_path)
        
        # 根据问题规模和剩余时间动态调整扰动次数
        n = len(best_path)
        remaining_time = end_time - time.time()
        
        if remaining_time <= 0:
            break  # 如果已经没有时间了，直接退出
            
        # 根据问题规模和剩余时间调整扰动次数
        if n > 50:
            # 大规模问题
            if remaining_time < 30:
                actual_perturbation_moves = 1  # 时间不足时只做一次扰动
            else:
                actual_perturbation_moves = min(perturbation_moves, 3)  # 最多3次扰动
        elif n > 30:
            # 中等规模问题
            actual_perturbation_moves = min(perturbation_moves, 5)
        else:
            # 小规模问题
            actual_perturbation_moves = perturbation_moves
        
        for p_move in range(actual_perturbation_moves):   
            # 检查是否超时 - 每次扰动前检查
            if time.time() >= end_time:
                break
            
            # 记录扰动开始时间
            perturb_start = time.time()
            time_tracker.start_component_timer('perturbation')
            
            # 再次检查是否超时 - 确保在调用耗时函数前检查
            if time.time() >= end_time:
                time_tracker.end_component_timer('perturbation')
                break
                
            # 使用增强版扰动函数，传入距离矩阵、迭代信息、已知解和结束时间
            new_path = perturb_reorder(
                path=best_path, 
                dis_m=D, 
                iteration=iter, 
                max_iterations=1000,
                strategy='auto',  # 自动选择最合适的策略
                known_solutions=res_tours,  # 传入已知的最优解
                end_time=end_time  # 传递结束时间
            )
            
            # 扰动操作后清理缓存，防止多次扰动后缓存过度增长
            try:
                from optimized_topology_aware_perturbation import limit_cache_size, segment_cache
                
                # 根据问题规模动态调整缓存大小
                dynamic_cache_size = 1000
                if n > 100:  # 大规模问题
                    dynamic_cache_size = 800
                elif n < 50:  # 小规模问题
                    dynamic_cache_size = 1200
                
                # 每次扰动后有10%的概率清理缓存，避免频繁清理影响性能
                if random.random() < 0.1 and len(segment_cache) > dynamic_cache_size * 0.5:
                    force_cleanup = len(segment_cache) > dynamic_cache_size * 0.8
                    limit_cache_size(max_size=dynamic_cache_size, force_cleanup=force_cleanup)
            except (ImportError, AttributeError):
                # 如果导入失败或属性不存在，说明没有使用优化的拓扑感知扰动
                pass
            except Exception as e:
                print(f"缓存清理失败: {str(e)}")
                # 继续执行，不影响主流程
            # 累计扰动时间
            elapsed_time = time.time() - perturb_start
            phase_times['perturbation'] += elapsed_time
            time_tracker.end_component_timer('perturbation')
            
            cur_route = np.array(tour2route(new_path), dtype=np.int64)
            D = np.array(D)  # maintain original dtype for distance matrix
            N = np.array(N, dtype=np.int64)
            
            # 记录two_opt开始时间
            two_opt_start = time.time()
            time_tracker.start_component_timer('two_opt')
            delta, new_tour = gls_operators.two_opt_a2a(cur_route, D, N, first_improvement)#2_opt局部搜索算子改进解若路径变短继续循环进行改进
            if delta < 0 or (delta==0 and not np.array_equal(new_tour, cur_route)):
                improved = True
                cur_cost = tour_cost_2End(D,new_tour)
                cur_route = new_tour
            # 累计