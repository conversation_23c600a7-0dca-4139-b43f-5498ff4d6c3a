import numpy as np
import time
import gls_operators
import gls_evol_enhanced

# # 导入LKH算法组件
# try:
#     from lkh_components import generate_candidate_set, initialize_dont_look_bits, sequential_k_opt, lkh_search
#     lkh_available = True
# except ImportError:
#     lkh_available = False
#     print("警告: LKH组件不可用，将跳过相关函数的预热")

# 导入统一路径相似度模块
try:
    import path_similarity_optimizer
    path_similarity_available = True
except ImportError:
    path_similarity_available = False
    print("警告: 路径相似度模块不可用，将跳过相关函数的预热")


def warmup_jit_functions():
    """
    预热所有带有@jit装饰器的函数，确保在实际使用前完成JIT编译
    这可以避免在实际计算过程中因首次调用JIT函数而导致的性能延迟
    """
    print("开始预热JIT编译函数...")
    start_time = time.time()
    
    # 创建小规模的模拟数据用于预热
    # 创建一个小的测试tour数组
    n = 20  # 小规模问题，足够触发JIT编译
    test_tour = np.zeros((n, 2), dtype=np.int64)
    for i in range(n):
        test_tour[i, 0] = (i - 1) % n  # 前一个节点
        test_tour[i, 1] = (i + 1) % n  # 后一个节点
    
    # 创建一个随机距离矩阵
    test_D = np.random.rand(n, n)
    np.fill_diagonal(test_D, 0)  # 对角线元素为0
    test_D = (test_D + test_D.T) / 2  # 确保矩阵对称
    
    # 创建一个邻居矩阵
    test_N = [np.argsort(test_D[i])[1:min(10, n)] for i in range(n)]
    
    # 创建一个测试路径
    test_path = np.arange(n)
    np.random.shuffle(test_path)  # 随机打乱
    
    print("1. 预热gls_operators模块的JIT函数...")
    
    # 预热two_opt函数
    print("  预热two_opt函数...")
    i, j = 1, 5
    _ = gls_operators.two_opt(test_tour.copy(), i, j)
    
    # 预热two_opt_cost函数
    print("  预热two_opt_cost函数...")
    _ = gls_operators.two_opt_cost(test_tour, test_D, i, j)
    
    # 预热two_opt_a2a函数
    print("  预热two_opt_a2a函数...")
    _ = gls_operators.two_opt_a2a(test_tour.copy(), test_D, test_N, False)
    
    # 预热relocate函数
    print("  预热relocate函数...")
    _ = gls_operators.relocate(test_tour.copy(), i, j)
    
    # 预热relocate_cost函数
    print("  预热relocate_cost函数...")
    _ = gls_operators.relocate_cost(test_tour, test_D, i, j)
    
    # 预热relocate_a2a函数
    print("  预热relocate_a2a函数...")
    _ = gls_operators.relocate_a2a(test_tour.copy(), test_D, test_N, False)
    
    print("2. 预热gls_evol_enhanced模块的JIT函数...")
    
    # 预热tour_cost函数
    print("  预热tour_cost函数...")
    _ = gls_evol_enhanced.tour_cost(test_D, test_path)
    
    # 预热tour_cost_2End函数
    print("  预热tour_cost_2End函数...")
    _ = gls_evol_enhanced.tour_cost_2End(test_D, test_tour)
    
    # 预热route2tour函数
    print("  预热route2tour函数...")
    _ = gls_evol_enhanced.route2tour(test_tour)
    
    # 预热tour2route函数
    print("  预热tour2route函数...")
    _ = gls_evol_enhanced.tour2route(test_path)
    
    # 预热normalize_path函数
    print("  预热normalize_path函数...")
    _ = gls_evol_enhanced.normalize_path(test_path)
    
    # # 预热LKH组件中的JIT函数
    # if lkh_available:
    #     print("3. 预热lkh_components模块的JIT函数...")
        
    #     # 预热generate_candidate_set函数
    #     print("  预热generate_candidate_set函数...")
    #     _ = generate_candidate_set(test_D, 5)
        
    #     # 预热initialize_dont_look_bits函数
    #     print("  预热initialize_dont_look_bits函数...")
    #     _ = initialize_dont_look_bits(n)
        
    #     # 预热sequential_k_opt函数
    #     print("  预热sequential_k_opt函数...")
    #     try:
    #         _ = sequential_k_opt(test_path.copy(), test_D, 3, None)
    #     except Exception as e:
    #         print(f"    警告: sequential_k_opt预热失败: {e}")
    
    # 预热统一路径相似度模块中的JIT函数
    if path_similarity_available:
        print("4. 预热path_similarity_optimizer模块的JIT函数...")
        try:
            # 预热calculate_edge_similarity_jit函数
            print("  预热calculate_edge_similarity_jit函数...")
            path1 = test_path.copy()
            path2 = np.roll(test_path.copy(), 1)  # 创建一个稍微不同的路径
            _ = path_similarity_optimizer.calculate_edge_similarity_jit(path1, path2)

            # 预热batch_calculate_similarity_jit函数
            print("  预热batch_calculate_similarity_jit函数...")
            paths = [np.roll(test_path.copy(), i) for i in range(5)]  # 创建多个测试路径
            _ = path_similarity_optimizer.batch_calculate_similarity_jit(test_path, np.array(paths))
        except Exception as e:
            print(f"    警告: path_similarity_optimizer预热失败: {e}")
    
    # 计算并显示预热时间
    elapsed_time = time.time() - start_time
    print(f"JIT函数预热完成，耗时: {elapsed_time:.2f}秒")


if __name__ == "__main__":
    # 直接运行此文件可以测试预热功能
    warmup_jit_functions()